import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../models/word.dart';
import '../../widgets/common/loading_indicator.dart';

/// Kelime detay sayfası
/// Performans odaklı ve modüler yapıda tasarlanmıştır
class WordDetailPage extends StatefulWidget {
  final int wordId;

  const WordDetailPage({
    super.key,
    required this.wordId,
  });

  @override
  State<WordDetailPage> createState() => _WordDetailPageState();
}

class _WordDetailPageState extends State<WordDetailPage> {
  Word? _word;
  bool _isLoading = true;
  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _loadWordDetails();
  }

  Future<void> _loadWordDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Simulated word loading - replace with actual service call
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Create a dummy word for now
      _word = Word(
        id: widget.wordId,
        entryId: widget.wordId,
        kanjiForms: ['漢字'],
        kanaForms: ['かんじ'],
        japanese: '漢字',
        romaji: 'kanji',
        meaningsTr: ['Çince karakter'],
        meaningsEn: ['Chinese character'],
        jlptLevel: 5,
        isActive: true,

      );
      
      _isFavorite = false; // Check if word is favorite
    } catch (e) {
      // Handle error
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _toggleFavorite() async {
    if (_word == null) return;

    setState(() {
      _isFavorite = !_isFavorite;
    });

    try {
      // Update favorite status in database
      // await databaseService.updateWordFavoriteStatus(_word!.id, _isFavorite);
    } catch (e) {
      // Revert on error
      setState(() {
        _isFavorite = !_isFavorite;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_word?.japanese ?? 'word_detail'.tr()),
        actions: [
          if (_word != null)
            IconButton(
              onPressed: _toggleFavorite,
              icon: Icon(
                _isFavorite ? Icons.favorite : Icons.favorite_border,
                color: _isFavorite ? Colors.red : null,
              ),
            ),
        ],
      ),
      body: _isLoading
          ? const LoadingIndicator()
          : _word == null
              ? _buildErrorState()
              : _buildWordContent(),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'word_not_found'.tr(),
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('go_back'.tr()),
          ),
        ],
      ),
    );
  }

  Widget _buildWordContent() {
    final word = _word!;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMainWordCard(word),
          const SizedBox(height: 16),
          _buildMeaningsSection(word),
          const SizedBox(height: 16),
          _buildDetailsSection(word),
        ],
      ),
    );
  }

  Widget _buildMainWordCard(Word word) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Japanese text
            Text(
              word.japanese,
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            
            // Romaji
            if (word.romaji.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                word.romaji,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontStyle: FontStyle.italic,
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
                ),
              ),
            ],

            // JLPT Level
            if (word.jlptLevel != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.secondary,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  'JLPT N${word.jlptLevel}',
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onSecondary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMeaningsSection(Word word) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'meanings'.tr(),
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            // Turkish meanings
            if (word.meaningsTr.isNotEmpty) ...[
              Text(
                'turkish'.tr(),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              const SizedBox(height: 8),
              ...word.meaningsTr.asMap().entries.map((entry) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('${entry.key + 1}. '),
                    Expanded(
                      child: Text(
                        entry.value,
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ),
                  ],
                ),
              )),
              const SizedBox(height: 16),
            ],

            // English meanings
            if (word.meaningsEn.isNotEmpty) ...[
              Text(
                'english'.tr(),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              const SizedBox(height: 8),
              ...word.meaningsEn.asMap().entries.map((entry) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('${entry.key + 1}. '),
                    Expanded(
                      child: Text(
                        entry.value,
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ),
                  ],
                ),
              )),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsSection(Word word) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'details'.tr(),
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            // Kanji forms
            if (word.kanjiForms.isNotEmpty) ...[
              _buildDetailRow('kanji_forms'.tr(), word.kanjiForms.join(', ')),
              const SizedBox(height: 8),
            ],

            // Kana forms
            if (word.kanaForms.isNotEmpty) ...[
              _buildDetailRow('kana_forms'.tr(), word.kanaForms.join(', ')),
              const SizedBox(height: 8),
            ],

            // Entry ID
            _buildDetailRow('entry_id'.tr(), word.entryId.toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }
}
