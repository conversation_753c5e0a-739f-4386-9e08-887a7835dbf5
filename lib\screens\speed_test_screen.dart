import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'dart:math';
import '../screens/cards_tab.dart';
import '../utils/app_logger.dart';
import '../services/enhanced_statistics_service.dart';
import '../services/tts_service.dart';
import '../models/word.dart';
import '../models/category_info.dart';
import '../services/audio_service.dart';
import '../services/user_progress_service.dart';
import '../services/stats_service.dart';

enum SpeedTestType {
  kanjiToMeaning,
  meaningToKanji,
  kanjiToReading,
  readingToKanji,
}

class SpeedTestQuestion {
  final int id;
  final String kanji;
  final String correctAnswer;
  final List<String> options;
  final SpeedTestType type;

  SpeedTestQuestion({
    required this.id,
    required this.kanji,
    required this.correctAnswer,
    required this.options,
    required this.type,
  });
}

class SpeedTestResult {
  final int questionId;
  final String question;
  final String selectedAnswer;
  final String correctAnswer;
  final bool isCorrect;
  final int responseTime;
  final bool wasSkipped;

  SpeedTestResult({
    required this.questionId,
    required this.question,
    required this.selectedAnswer,
    required this.correctAnswer,
    required this.isCorrect,
    required this.responseTime,
    required this.wasSkipped,
  });
}

class SpeedTestScreen extends StatefulWidget {
  final String category;
  final SpeedTestType testType;
  final int questionCount;
  final int timePerQuestion;
  final List<Word>? words;

  const SpeedTestScreen({
    super.key,
    required this.category,
    this.testType = SpeedTestType.kanjiToMeaning,
    this.questionCount = 20,
    this.timePerQuestion = 5,
    this.words,
  });

  @override
  State<SpeedTestScreen> createState() => _SpeedTestScreenState();
}

class _SpeedTestScreenState extends State<SpeedTestScreen>
    with TickerProviderStateMixin {
  
  // Test durumu
  List<SpeedTestQuestion> _questions = [];
  int _currentQuestionIndex = 0;
  int _correctAnswers = 0;
  int _wrongAnswers = 0;
  int _skippedAnswers = 0;
  bool _isTestFinished = false;
  
  // Timer
  Timer? _timer;
  int _remainingTime = 0;
  late AnimationController _timerController;
  late Animation<double> _timerAnimation;
  
  // Card animasyonları
  late AnimationController _cardController;
  late AnimationController _scoreController;
  late Animation<double> _cardFlipAnimation;
  late Animation<double> _scoreAnimation;
  late Animation<Offset> _slideAnimation;
  
  // Services
  final TtsService _ttsService = TtsService();
  
  // Test sonuçları
  List<SpeedTestResult> _results = [];
  DateTime? _testStartTime;
  
  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _generateQuestions();
    _startTest();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _timerController.dispose();
    _cardController.dispose();
    _scoreController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _timerController = AnimationController(
      duration: Duration(seconds: widget.timePerQuestion),
      vsync: this,
    );
    _timerAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _timerController, curve: Curves.linear),
    );

    _cardController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _cardFlipAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _cardController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _cardController, curve: Curves.easeOut));

    _scoreController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _scoreAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _scoreController, curve: Curves.elasticOut),
    );
  }

  void _generateQuestions() {
    // Mock data - gerçek implementasyonda kanji veritabanından gelecek
    final mockKanji = [
      {'kanji': '日', 'meaning': 'Gün, Güneş', 'reading': 'ひ'},
      {'kanji': '月', 'meaning': 'Ay', 'reading': 'つき'},
      {'kanji': '火', 'meaning': 'Ateş', 'reading': 'ひ'},
      {'kanji': '水', 'meaning': 'Su', 'reading': 'みず'},
      {'kanji': '木', 'meaning': 'Ağaç', 'reading': 'き'},
      {'kanji': '金', 'meaning': 'Altın, Metal', 'reading': 'きん'},
      {'kanji': '土', 'meaning': 'Toprak', 'reading': 'つち'},
      {'kanji': '人', 'meaning': 'İnsan', 'reading': 'ひと'},
      {'kanji': '大', 'meaning': 'Büyük', 'reading': 'おおき'},
      {'kanji': '小', 'meaning': 'Küçük', 'reading': 'ちいさ'},
    ];

    _questions = List.generate(widget.questionCount, (index) {
      final kanjiData = mockKanji[index % mockKanji.length];
      return SpeedTestQuestion(
        id: index,
        kanji: kanjiData['kanji']!,
        correctAnswer: _getCorrectAnswer(kanjiData),
        options: _generateOptions(kanjiData),
        type: widget.testType,
      );
    });
  }

  String _getCorrectAnswer(Map<String, String> kanjiData) {
    switch (widget.testType) {
      case SpeedTestType.kanjiToMeaning:
        return kanjiData['meaning']!;
      case SpeedTestType.meaningToKanji:
        return kanjiData['kanji']!;
      case SpeedTestType.kanjiToReading:
        return kanjiData['reading']!;
      case SpeedTestType.readingToKanji:
        return kanjiData['kanji']!;
    }
  }

  List<String> _generateOptions(Map<String, String> kanjiData) {
    final correct = _getCorrectAnswer(kanjiData);
    final wrongOptions = ['Yanlış 1', 'Yanlış 2', 'Yanlış 3'];
    final options = [correct, ...wrongOptions];
    options.shuffle();
    return options;
  }

  void _startTest() {
    _testStartTime = DateTime.now();
    _remainingTime = widget.timePerQuestion;
    _startQuestionTimer();
    _cardController.forward();
  }

  void _startQuestionTimer() {
    _timerController.reset();
    _timerController.forward();
    
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingTime > 0) {
        setState(() {
          _remainingTime--;
        });
      } else {
        _onTimeUp();
      }
    });
  }

  void _onTimeUp() {
    _timer?.cancel();
    _onAnswer(null, true); // Zaman aştı
  }

  void _onAnswer(String? selectedAnswer, [bool timeUp = false]) {
    _timer?.cancel();
    
    final question = _questions[_currentQuestionIndex];
    final isCorrect = !timeUp && selectedAnswer == question.correctAnswer;
    
    final result = SpeedTestResult(
      questionId: question.id,
      question: question.kanji,
      selectedAnswer: selectedAnswer ?? '',
      correctAnswer: question.correctAnswer,
      isCorrect: isCorrect,
      responseTime: widget.timePerQuestion - _remainingTime,
      wasSkipped: timeUp,
    );
    
    _results.add(result);
    
    if (isCorrect) {
      _correctAnswers++;
      _scoreController.forward().then((_) => _scoreController.reverse());
    } else if (timeUp) {
      _skippedAnswers++;
    } else {
      _wrongAnswers++;
    }

    // Ses feedback
    if (isCorrect) {
      _playSuccessSound();
    } else {
      _playErrorSound();
    }

    _nextQuestion();
  }

  void _nextQuestion() {
    if (_currentQuestionIndex < widget.questionCount - 1) {
      setState(() {
        _currentQuestionIndex++;
        _remainingTime = widget.timePerQuestion;
      });
      _cardController.reset();
      _cardController.forward();
      _startQuestionTimer();
    } else {
      _finishTest();
    }
  }

  void _finishTest() {
    _timer?.cancel();
    setState(() {
      _isTestFinished = true;
    });
    _saveResults();
  }

  void _saveResults() async {
    try {
      final statisticsService = Provider.of<EnhancedStatisticsService>(context, listen: false);
      
      final testDuration = DateTime.now().difference(_testStartTime!);
      final responseTimesMs = _results.map((r) => r.responseTime * 1000).toList();
      
      await statisticsService.recordStudySession(
        type: StudySessionType.speedTest,
        duration: testDuration,
        totalQuestions: widget.questionCount,
        correctAnswers: _correctAnswers,
        responseTimesMs: responseTimesMs,
        additionalData: {
          'testType': widget.testType.toString(),
          'averageResponseTime': responseTimesMs.isNotEmpty 
            ? responseTimesMs.reduce((a, b) => a + b) / responseTimesMs.length 
            : 0,
          'skippedQuestions': _skippedAnswers,
          'accuracy': _correctAnswers / widget.questionCount,
        },
      );
      
      AppLogger.info('Speed test results saved successfully');
    } catch (e, s) {
      AppLogger.error('Failed to save speed test results', e, s);
    }
  }

  void _playSuccessSound() {
    // Başarı sesi çal
  }

  void _playErrorSound() {
    // Hata sesi çal
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: Colors.blue,
      appBar: AppBar(
        title: Text('speed_kanji_test'.tr()),
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (!_isTestFinished)
            Container(
              margin: const EdgeInsets.only(right: 16),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                '${_currentQuestionIndex + 1}/${widget.questionCount}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: _isTestFinished ? _buildResultScreen() : _buildTestScreen(),
    );
  }

  Widget _buildTestScreen() {
    return Column(
      children: [
        // Timer ve Progress
        _buildTimerSection(),
        
        // Skor
        _buildScoreSection(),
        
        // Ana Kart
        Expanded(
          child: _buildQuestionCard(),
        ),
        
        // Kontrol butonları
        _buildControlButtons(),
      ],
    );
  }

  Widget _buildTimerSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Timer Circle
          AnimatedBuilder(
            animation: _timerAnimation,
            builder: (context, child) {
              return Stack(
                alignment: Alignment.center,
                children: [
                  SizedBox(
                    width: 80,
                    height: 80,
                    child: CircularProgressIndicator(
                      value: _timerAnimation.value,
                      strokeWidth: 6,
                      backgroundColor: Colors.white.withOpacity(0.3),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        _remainingTime <= 2 ? Colors.red : Colors.white,
                      ),
                    ),
                  ),
                  Text(
                    '$_remainingTime',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              );
            },
          ),
          
          const SizedBox(height: 16),
          
          // Progress Bar
          LinearProgressIndicator(
            value: (_currentQuestionIndex + 1) / widget.questionCount,
            backgroundColor: Colors.white.withOpacity(0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildScoreSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildScoreItem(
            '✓',
            _correctAnswers.toString(),
            Colors.green,
          ),
          _buildScoreItem(
            '✗',
            _wrongAnswers.toString(),
            Colors.red,
          ),
          _buildScoreItem(
            '⏭',
            _skippedAnswers.toString(),
            Colors.orange,
          ),
        ],
      ),
    );
  }

  Widget _buildScoreItem(String icon, String count, Color color) {
    return AnimatedBuilder(
      animation: _scoreAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: 1.0 + (_scoreAnimation.value * 0.2),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  icon,
                  style: TextStyle(
                    color: color,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  count,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuestionCard() {
    if (_questions.isEmpty) return const Center(child: CircularProgressIndicator());
    
    final question = _questions[_currentQuestionIndex];
    
    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        margin: const EdgeInsets.all(20),
        child: Card(
          elevation: 10,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            padding: const EdgeInsets.all(30),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                colors: [Colors.white, Colors.grey[50]!],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Soru tipi label
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    _getQuestionTypeLabel(),
                    style: TextStyle(
                      color: Colors.blue,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                
                const SizedBox(height: 30),
                
                // Ana soru
                Text(
                  _getQuestionText(question),
                  style: const TextStyle(
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 40),
                
                // Cevap seçenekleri
                ...question.options.asMap().entries.map((entry) {
                  final index = entry.key;
                  final option = entry.value;
                  
                  return Container(
                    width: double.infinity,
                    margin: const EdgeInsets.only(bottom: 12),
                    child: ElevatedButton(
                      onPressed: () => _onAnswer(option),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.all(16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        '${String.fromCharCode(65 + index)}. $option',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getQuestionTypeLabel() {
    switch (widget.testType) {
      case SpeedTestType.kanjiToMeaning:
        return 'Kanji → Anlam';
      case SpeedTestType.meaningToKanji:
        return 'Anlam → Kanji';
      case SpeedTestType.kanjiToReading:
        return 'Kanji → Okunuş';
      case SpeedTestType.readingToKanji:
        return 'Okunuş → Kanji';
    }
  }

  String _getQuestionText(SpeedTestQuestion question) {
    switch (widget.testType) {
      case SpeedTestType.kanjiToMeaning:
      case SpeedTestType.kanjiToReading:
        return question.kanji;
      case SpeedTestType.meaningToKanji:
        return question.correctAnswer; // meaning
      case SpeedTestType.readingToKanji:
        return question.correctAnswer; // reading
    }
  }

  Widget _buildControlButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ElevatedButton.icon(
            onPressed: () => _onAnswer(null, true),
            icon: const Icon(Icons.skip_next),
            label: Text('skip'.tr()),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
          ),
          
          ElevatedButton.icon(
            onPressed: () {
              _ttsService.speak(_questions[_currentQuestionIndex].kanji);
            },
            icon: const Icon(Icons.volume_up),
            label: Text('listen'.tr()),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultScreen() {
    final accuracy = (_correctAnswers / widget.questionCount * 100).round();
    final averageTime = _results.isNotEmpty 
      ? _results.map((r) => r.responseTime).reduce((a, b) => a + b) / _results.length
      : 0.0;
    
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue, Colors.blueAccent],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Ana Skor
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withOpacity(0.9),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '$accuracy%',
                    style: TextStyle(
                      fontSize: 48,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  Text(
                    'doğruluk',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 40),
            
            // İstatistikler
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 30),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Column(
                children: [
                  _buildResultRow('Doğru Cevaplar', '$_correctAnswers/${widget.questionCount}'),
                  _buildResultRow('Ortalama Süre', '${averageTime.toStringAsFixed(1)}s'),
                  _buildResultRow('Toplam Süre', '${(_results.length * widget.timePerQuestion)}s'),
                  _buildResultRow('Hızlı Cevaplar', '${_results.where((r) => r.responseTime < 3).length}'),
                ],
              ),
            ),
            
            const SizedBox(height: 40),
            
            // Butonlar
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.home),
                  label: Text('Ana Sayfa'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.blue,
                  ),
                ),
                
                ElevatedButton.icon(
                  onPressed: _restartTest,
                  icon: const Icon(Icons.refresh),
                  label: Text('Tekrar Dene'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.blue,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  void _restartTest() {
    setState(() {
      _currentQuestionIndex = 0;
      _correctAnswers = 0;
      _wrongAnswers = 0;
      _skippedAnswers = 0;
      _isTestFinished = false;
      _results.clear();
    });
    _generateQuestions();
    _startTest();
  }
} 