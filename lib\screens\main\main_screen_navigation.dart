import 'package:flutter/material.dart';

// Import necessary screens for the bottom navigation
import '../home_tab.dart';
import '../favorites_page.dart';
import '../settings_screen.dart';
import '../dictionary_tab.dart';
import '../cards/cards_tab_main.dart';
import '../achievements_screen.dart';
import '../statistics_hub_screen.dart';
import '../simple_translation_screen.dart';
import '../kanji_screen.dart';
import '../premium_page.dart';

/// Ana ekran navigasyon yöneticisi - performans odaklı
class MainScreenNavigation {
  
  /// Widget seçeneklerini oluştur
  Future<List<Widget>> createWidgetOptions() async {
    // Background'da widget'ları oluştur - Bottom navigation ile eşleşen 4 tab + Translation hızlı erişim
    return [
      const HomeTab(),        // 0: home
      const CardsTabMain(),   // 1: cards (kelime kartları + JLPT)
      const DictionaryTab(),  // 2: dictionary (cü<PERSON>le çeviri + dil çiftleri)
      const SettingsScreen(), // 3: settings
    ];
  }

  /// Premium sayfasına git
  void navigateToPremium(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const PremiumPage()),
    );
  }

  /// Çeviri sayfasına git
  void navigateToTranslation(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SimpleTranslationScreen()),
    );
  }

  /// Kanji sayfasına git
  void navigateToKanji(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const KanjiScreen()),
    );
  }

  /// Favoriler sayfasına git
  void navigateToFavorites(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const FavoritesPage()),
    );
  }

  /// Başarılar sayfasına git
  void navigateToAchievements(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AchievementsScreen()),
    );
  }

  /// İstatistikler sayfasına git
  void navigateToStatistics(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const StatisticsHubScreen()),
    );
  }

  /// Oyunlar sayfasına git
  void navigateToGames(BuildContext context) {
    // Games tab zaten mevcut, index değiştir
  }

  /// Sözlük sayfasına git
  void navigateToDictionary(BuildContext context) {
    // Dictionary tab zaten mevcut, index değiştir
  }

  /// Ayarlar sayfasına git
  void navigateToSettings(BuildContext context) {
    // Settings tab zaten mevcut, index değiştir
  }

  /// Geri git
  void goBack(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }

  /// Ana sayfaya git
  void navigateToHome(BuildContext context) {
    Navigator.popUntil(context, (route) => route.isFirst);
  }

  /// Performans odaklı sayfa geçişi
  PageRouteBuilder createPerformantRoute(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.ease;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }

  /// Modal bottom sheet göster
  void showCustomModalBottomSheet(
    BuildContext context, {
    required Widget child,
    bool isScrollControlled = false,
  }) {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: isScrollControlled,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: child,
      ),
    );
  }

  /// Dialog göster
  Future<T?> showCustomDialog<T>(
    BuildContext context, {
    required Widget child,
    bool barrierDismissible = true,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: child,
      ),
    );
  }

  /// Snackbar göster
  void showSnackBar(
    BuildContext context, {
    required String message,
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: duration,
        action: action,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  /// Hata mesajı göster
  void showErrorMessage(
    BuildContext context, {
    required String message,
    VoidCallback? onRetry,
  }) {
    showSnackBar(
      context,
      message: message,
      duration: const Duration(seconds: 5),
      action: onRetry != null
          ? SnackBarAction(
              label: 'Tekrar Dene',
              onPressed: onRetry,
            )
          : null,
    );
  }

  /// Başarı mesajı göster
  void showSuccessMessage(
    BuildContext context, {
    required String message,
  }) {
    showSnackBar(
      context,
      message: message,
      duration: const Duration(seconds: 2),
    );
  }

  /// Bilgi mesajı göster
  void showInfoMessage(
    BuildContext context, {
    required String message,
  }) {
    showSnackBar(
      context,
      message: message,
      duration: const Duration(seconds: 3),
    );
  }
}
