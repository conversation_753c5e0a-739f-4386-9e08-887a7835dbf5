import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:provider/provider.dart';
import '../../services/kelime_service.dart';
import 'package:japoni_go/utils/app_logger.dart';
import '../../services/user_progress_service.dart';
import '../../services/kanji_service.dart';
import '../../services/level_service.dart';
import '../../services/difficulty_algorithm_service.dart';
import '../../services/enhanced_statistics_service.dart';
import '../../services/enhanced_kanji_learning_service.dart';
import '../../services/audio_service.dart';
import '../../services/haptic_service.dart';
import '../flashcard_screen.dart';
import 'package:japoni_go/widgets/loading_indicator.dart';
import 'package:japoni_go/models/word.dart';
import 'package:japoni_go/models/grammar_rule.dart';
import 'package:japoni_go/models/category_info.dart';
import 'package:japoni_go/widgets/animated_category_card.dart';
import 'package:japoni_go/widgets/page_transitions.dart';
import '../radicals_learning_screen.dart';
import '../kanji_recognition_test_screen.dart';
import '../level_progression_screen.dart';
import '../../widgets/kanji_level_selector_widget.dart';
import '../../widgets/custom_app_bar.dart';
import '../vocabulary_screen.dart';
import '../grammar_screen.dart';
import '../jlpt_level_screen.dart';
import 'cards_tab_services.dart';
import 'cards_tab_data.dart';
import 'cards_tab_widgets.dart';
import 'package:japoni_go/services/word_service.dart';

class CardsTabMain extends StatelessWidget {
  const CardsTabMain({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // This is a placeholder list. In a real app, this would come from a configuration service.
    final List<CategoryInfo> categories = [
      CategoryInfo(id: 'jlpt_n5', name: 'JLPT N5', description: 'Beginner level', wordCount: 800, iconPath: '', difficulty: 'Beginner', primaryColor: const Color(0xFF4CAF50), secondaryColor: const Color(0xFF81C784), titleKey: 'jlpt_n5', type: 'jlpt', subType: 'N5', icon: Icons.school),
      CategoryInfo(id: 'jlpt_n4', name: 'JLPT N4', description: 'Elementary level', wordCount: 1500, iconPath: '', difficulty: 'Elementary', primaryColor: const Color(0xFF2196F3), secondaryColor: const Color(0xFF64B5F6), titleKey: 'jlpt_n4', type: 'jlpt', subType: 'N4', icon: Icons.school),
      CategoryInfo(id: 'jlpt_n3', name: 'JLPT N3', description: 'Intermediate level', wordCount: 3000, iconPath: '', difficulty: 'Intermediate', primaryColor: const Color(0xFFFF9800), secondaryColor: const Color(0xFFFFB74D), titleKey: 'jlpt_n3', type: 'jlpt', subType: 'N3', icon: Icons.school),
      CategoryInfo(id: 'common_words', name: 'Common Words', description: 'Everyday vocabulary', wordCount: 1000, iconPath: '', difficulty: 'Beginner', primaryColor: const Color(0xFF9C27B0), secondaryColor: const Color(0xFFBA68C8), titleKey: 'common_words', type: 'theme', subType: 'general', icon: Icons.chat_bubble),
      CategoryInfo(id: 'travel', name: 'Travel', description: 'Travel vocabulary', wordCount: 500, iconPath: '', difficulty: 'Elementary', primaryColor: const Color(0xFF607D8B), secondaryColor: const Color(0xFF90A4AE), titleKey: 'travel', type: 'theme', subType: 'travel', icon: Icons.flight),
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text('flashcard_categories'.tr()),
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(8.0),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          return Card(
            elevation: 2,
            margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
            child: ListTile(
              leading: Icon(category.icon, color: Theme.of(context).colorScheme.primary),
              title: Text(
                category.titleKey.tr(),
                style: Theme.of(context).textTheme.titleLarge,
              ),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    // FlashcardScreen no longer takes a category, it's self-contained.
                    builder: (context) => const FlashcardScreen(),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}