import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:provider/provider.dart';
import '../services/word_service.dart';
import '../services/audio_service.dart';
import '../services/haptic_service.dart';
import '../services/user_progress_service.dart';
import '../models/word.dart';
import '../widgets/loading_indicator.dart';
import '../utils/app_logger.dart';
import 'quiz_screen.dart';
import 'word_detail_page.dart';
import 'speed_test_screen.dart';
import 'flashcard_screen.dart';
import '../models/category_info.dart';

class VocabularyScreen extends StatefulWidget {
  final String? category;
  final String? title;
  
  const VocabularyScreen({super.key, this.category, this.title});

  @override
  State<VocabularyScreen> createState() => _VocabularyScreenState();
}

class _VocabularyScreenState extends State<VocabularyScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _headerAnimationController;
  late AnimationController _cardAnimationController;
  late Animation<double> _headerFadeAnimation;
  late Animation<Offset> _cardSlideAnimation;

  late WordService _wordService;
  late AudioService _audioService;
  late HapticService _hapticService;
  late UserProgressService _userProgressService;

  List<Word> _words = [];
  List<Word> _filteredWords = [];
  bool _isLoading = true;
  String _searchQuery = '';
  int _learnedWordsCount = 0;
  int _totalWordsCount = 0;
  String _selectedDifficulty = 'all';
  bool _showOnlyFavorites = false;

  final TextEditingController _searchController = TextEditingController();

  // Kategori renkleri
  Color get _categoryColor {
    switch (widget.category) {
      case 'family': return const Color(0xFFE91E63);
      case 'numbers': return const Color(0xFF2196F3);
      case 'food': return const Color(0xFFFF9800);
      case 'travel': return const Color(0xFF4CAF50);
      case 'shopping': return const Color(0xFF9C27B0);
      case 'health': return const Color(0xFFF44336);
      default: return const Color(0xFF2196F3);
    }
  }

  Color get _categorySecondaryColor {
    switch (widget.category) {
      case 'family': return const Color(0xFFF48FB1);
      case 'numbers': return const Color(0xFF64B5F6);
      case 'food': return const Color(0xFFFFB74D);
      case 'travel': return const Color(0xFF66BB6A);
      case 'shopping': return const Color(0xFFCE93D8);
      case 'health': return const Color(0xFFEF5350);
      default: return const Color(0xFF64B5F6);
    }
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _headerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _cardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _headerFadeAnimation = CurvedAnimation(
      parent: _headerAnimationController,
      curve: Curves.easeInOut,
    );

    _cardSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _cardAnimationController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _wordService = Provider.of<WordService>(context, listen: false);
    _audioService = Provider.of<AudioService>(context, listen: false);
    _hapticService = Provider.of<HapticService>(context, listen: false);
    _userProgressService = Provider.of<UserProgressService>(context, listen: false);
    _loadVocabularyData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _headerAnimationController.dispose();
    _cardAnimationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadVocabularyData() async {
    try {
      setState(() => _isLoading = true);

      // Kategori kelimelerini yükle
      final words = await _wordService.getAllWords(level: widget.category);
      
      // İlerleme verilerini yükle
      final wordIds = words.map((w) => w.id.toString()).toList();
      final learnedWords = await _userProgressService.getLearnedCountForWordIds(wordIds);

      if (mounted) {
        setState(() {
          _words = words;
          _filteredWords = words;
          _totalWordsCount = words.length;
          _learnedWordsCount = learnedWords;
          _isLoading = false;
        });

        // Animasyonları başlat
        _headerAnimationController.forward();
        _cardAnimationController.forward();
      }
    } catch (e, s) {
      AppLogger.error('Vocabulary verileri yüklenirken hata', e, s);
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _filterWords() {
    setState(() {
      _filteredWords = _words.where((word) {
        // Arama filtresi
        bool matchesSearch = _searchQuery.isEmpty ||
            word.japanese.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            (word.meaningsTr.isNotEmpty ? word.meaningsTr.join(', ') : '').toLowerCase().contains(_searchQuery.toLowerCase()) ||
            word.romaji.toLowerCase().contains(_searchQuery.toLowerCase());

        // Zorluk filtresi - Word modelinde zorluk alanı yok, şimdilik kaldırıldı.
        // bool matchesDifficulty = _selectedDifficulty == 'all' ||
        //     word.zorlukSeviyesi.toString() == _selectedDifficulty;

        // Favori filtresi
        bool matchesFavorites = !_showOnlyFavorites || word.isFavorite;

        return matchesSearch && matchesFavorites; // && matchesDifficulty;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : NestedScrollView(
              headerSliverBuilder: (context, innerBoxIsScrolled) {
                return [
                  _buildModernAppBar(),
                ];
              },
              body: TabBarView(
                controller: _tabController,
                children: [
                  _buildWordsTab(),
                  _buildPracticeTab(),
                  _buildStatsTab(),
                ],
              ),
            ),
    );
  }

  Widget _buildModernAppBar() {
    final double progress = _totalWordsCount > 0 ? _learnedWordsCount / _totalWordsCount : 0.0;

    return SliverAppBar(
      expandedHeight: 280,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: _categoryColor,
      foregroundColor: Colors.white,
      flexibleSpace: FlexibleSpaceBar(
        background: FadeTransition(
          opacity: _headerFadeAnimation,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  _categoryColor,
                  _categorySecondaryColor,
                ],
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 60, 16, 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Başlık ve ikon
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Icon(
                            _getCategoryIcon(),
                            color: Colors.white,
                            size: 32,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.title ?? 'Kelime Öğrenme',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 28,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                _getCategoryDescription(),
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.9),
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    
                    // İstatistik kartları
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            'Öğrenilen',
                            _learnedWordsCount.toString(),
                            Icons.check_circle,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildStatCard(
                            'Toplam',
                            _totalWordsCount.toString(),
                            Icons.library_books,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildStatCard(
                            'İlerleme',
                            '${(progress * 100).toInt()}%',
                            Icons.trending_up,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    // İlerleme çubuğu
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'Kategori İlerlemesi',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                '${_learnedWordsCount}/${_totalWordsCount}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          LinearProgressIndicator(
                            value: progress,
                            backgroundColor: Colors.white.withOpacity(0.3),
                            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                            minHeight: 8,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(48),
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(25),
          ),
          child: TabBar(
            controller: _tabController,
            indicator: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              color: Colors.white.withOpacity(0.3),
            ),
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            labelStyle: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
            tabs: const [
              Tab(
                icon: Icon(Icons.book, size: 18),
                text: 'Kelimeler',
              ),
              Tab(
                icon: Icon(Icons.quiz, size: 18),
                text: 'Pratik',
              ),
              Tab(
                icon: Icon(Icons.analytics, size: 18),
                text: 'İstatistik',
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getCategoryIcon() {
    switch (widget.category) {
      case 'family': return Icons.family_restroom;
      case 'numbers': return Icons.access_time;
      case 'food': return Icons.restaurant;
      case 'travel': return Icons.travel_explore;
      case 'shopping': return Icons.shopping_cart;
      case 'health': return Icons.health_and_safety;
      default: return Icons.book;
    }
  }

  String _getCategoryDescription() {
    switch (widget.category) {
      case 'family': return 'Aile üyeleri ve kişiler';
      case 'numbers': return 'Sayılar, zaman ve tarihler';
      case 'food': return 'Yemekler ve içecekler';
      case 'travel': return 'Seyahat ve yerler';
      case 'shopping': return 'Alışveriş ve para';
      case 'health': return 'Vücut ve sağlık';
      default: return 'Kelime öğrenme';
    }
  }

  Widget _buildStatCard(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWordsTab() {
    return SlideTransition(
      position: _cardSlideAnimation,
      child: Column(
        children: [
          _buildSearchAndFilters(),
          Expanded(
            child: _filteredWords.isEmpty
                ? _buildEmptyState()
                : _buildWordsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Arama çubuğu
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Kelime ara...',
              prefixIcon: Icon(Icons.search, color: _categoryColor),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                        _filterWords();
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: _categoryColor.withOpacity(0.3)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: _categoryColor, width: 2),
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
              _filterWords();
            },
          ),
          const SizedBox(height: 12),
          
          // Filtreler
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedDifficulty,
                  decoration: InputDecoration(
                    labelText: 'Zorluk',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('Tümü')),
                    DropdownMenuItem(value: '1', child: Text('Kolay')),
                    DropdownMenuItem(value: '2', child: Text('Orta')),
                    DropdownMenuItem(value: '3', child: Text('Zor')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedDifficulty = value!;
                    });
                    _filterWords();
                  },
                ),
              ),
              const SizedBox(width: 12),
              FilterChip(
                label: const Text('Favoriler'),
                selected: _showOnlyFavorites,
                onSelected: (selected) {
                  setState(() {
                    _showOnlyFavorites = selected;
                  });
                  _filterWords();
                },
                selectedColor: _categoryColor.withOpacity(0.3),
                checkmarkColor: _categoryColor,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWordsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredWords.length,
      itemBuilder: (context, index) {
        final word = _filteredWords[index];
        return TweenAnimationBuilder<double>(
          duration: Duration(milliseconds: 300 + (index * 50)),
          tween: Tween(begin: 0.0, end: 1.0),
          builder: (context, value, child) {
            return Transform.translate(
              offset: Offset(0, 30 * (1 - value)),
              child: Opacity(
                opacity: value,
                child: _buildWordCard(word, index),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildWordCard(Word word, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: _categoryColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          onTap: () {
            _audioService.playClickSound();
            _hapticService.lightImpact();
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => WordDetailPage(wordId: word.id),
              ),
            );
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            word.japanese,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Noto Sans JP',
                            ),
                          ),
                          if (word.romaji.isNotEmpty) ...[
                            const SizedBox(height: 4),
                            Text(
                              word.romaji,
                              style: TextStyle(
                                fontSize: 14,
                                fontStyle: FontStyle.italic,
                                color: _categoryColor,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: Icon(
                            Icons.volume_up,
                            color: _categoryColor,
                          ),
                          onPressed: () {
                            _audioService.playClickSound();
                            // TTS çal
                          },
                        ),
                        IconButton(
                          icon: Icon(
                            word.isFavorite ? Icons.favorite : Icons.favorite_border,
                            color: word.isFavorite ? Colors.red : Colors.grey,
                          ),
                          onPressed: () {
                            _audioService.playClickSound();
                            _hapticService.lightImpact();
                            // Favori durumunu değiştir
                          },
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  word.meaningsTr.join(', '),
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getDifficultyColor(word.jlptLevel ?? 5).withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getDifficultyText(word.jlptLevel ?? 5),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: _getDifficultyColor(word.jlptLevel ?? 5),
                        ),
                      ),
                    ),
                    const Spacer(),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: Colors.grey[400],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getDifficultyColor(int difficulty) {
    switch (difficulty) {
      case 1: return Colors.green;
      case 2: return Colors.orange;
      case 3: return Colors.red;
      default: return Colors.grey;
    }
  }

  String _getDifficultyText(int difficulty) {
    switch (difficulty) {
      case 1: return 'Kolay';
      case 2: return 'Orta';
      case 3: return 'Zor';
      default: return 'Bilinmiyor';
    }
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Kelime bulunamadı',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Farklı arama terimleri deneyin',
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPracticeTab() {
    return SlideTransition(
      position: _cardSlideAnimation,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    _categoryColor.withOpacity(0.1),
                    _categorySecondaryColor.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: _categoryColor.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: _categoryColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.fitness_center,
                      color: _categoryColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Pratik Egzersizleri',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: _categoryColor,
                          ),
                        ),
                        Text(
                          'Kelime bilginizi pekiştirin',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            
            // Practice cards
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                childAspectRatio: 1.1,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildPracticeCard(
                    'Quiz Pratiği',
                    'Kelime testleri',
                    Icons.quiz,
                    const Color(0xFF2196F3),
                    () {
                      _audioService.playClickSound();
                      _hapticService.mediumImpact();
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => QuizScreen(
                            words: _words,
                            quizTuru: 'vocabulary',
                            mode: QuizMode.wordToMeaning,
                            title: '${widget.title} Quiz',
                          ),
                        ),
                      );
                    },
                  ),
                  _buildPracticeCard(
                    'Hız Testi',
                    'Hızlı hatırlama',
                    Icons.speed,
                    const Color(0xFFFF5722),
                    () {
                      _audioService.playClickSound();
                      _hapticService.mediumImpact();
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => SpeedTestScreen(
                            category: widget.category ?? 'general',
                            words: _words,
                          ),
                        ),
                      );
                    },
                  ),
                  _buildPracticeCard(
                    'Flashcards',
                    'Kart çalışması',
                    Icons.style,
                    const Color(0xFF9C27B0),
                    () {
                      _audioService.playClickSound();
                      _hapticService.mediumImpact();
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const FlashcardScreen(),
                        ),
                      );
                    },
                  ),
                  _buildPracticeCard(
                    'Kelime Eşleştirme',
                    'Eşleştirme oyunu',
                    Icons.extension,
                    const Color(0xFF4CAF50),
                    () {
                      _audioService.playClickSound();
                      _hapticService.mediumImpact();
                      // Kelime eşleştirme oyunu
                      _showComingSoonDialog('Kelime Eşleştirme');
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPracticeCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 600),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * value),
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: color.withOpacity(0.2),
                  blurRadius: 12,
                  offset: const Offset(0, 6),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(20),
              child: InkWell(
                onTap: onTap,
                borderRadius: BorderRadius.circular(20),
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        color.withOpacity(0.1),
                        color.withOpacity(0.05),
                      ],
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: color.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Icon(
                          icon,
                          size: 32,
                          color: color,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatsTab() {
    return SlideTransition(
      position: _cardSlideAnimation,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Kategori İstatistikleri',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: _categoryColor,
              ),
            ),
            const SizedBox(height: 20),
            
            // İstatistik kartları
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                childAspectRatio: 1.2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildStatisticCard(
                    'Öğrenilen Kelimeler',
                    _learnedWordsCount.toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                  _buildStatisticCard(
                    'Kalan Kelimeler',
                    (_totalWordsCount - _learnedWordsCount).toString(),
                    Icons.pending,
                    Colors.orange,
                  ),
                  _buildStatisticCard(
                    'Favori Kelimeler',
                    _words.where((w) => w.isFavorite).length.toString(),
                    Icons.favorite,
                    Colors.red,
                  ),
                  _buildStatisticCard(
                    'Başarı Oranı',
                    '${(_totalWordsCount > 0 ? (_learnedWordsCount / _totalWordsCount * 100).toInt() : 0)}%',
                    Icons.trending_up,
                    _categoryColor,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showComingSoonDialog(String featureName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.construction,
              color: _categoryColor,
            ),
            const SizedBox(width: 8),
            const Text('Yakında Geliyor!'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('$featureName özelliği şu anda geliştiriliyor.'),
            const SizedBox(height: 16),
            Text(
              'Bu özellik yakında kullanıma sunulacak!',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Tamam'),
          ),
        ],
      ),
    );
  }
} 
