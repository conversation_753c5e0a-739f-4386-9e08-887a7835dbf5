import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:provider/provider.dart';
import '../services/word_service.dart';
import '../services/kanji_service.dart';
import 'dictionary/word_detail_page.dart';
import '../services/user_progress_service.dart';
import '../services/audio_service.dart';
import '../services/haptic_service.dart';
import '../models/word.dart';
import '../models/kanji.dart';
import '../widgets/loading_indicator.dart';
import '../utils/app_logger.dart';
import 'kanji_detail_screen.dart';
import 'quiz_screen.dart';
import 'speed_test_screen.dart';
import 'flashcard_screen.dart';
import 'kanji_recognition_test_screen.dart';
import '../models/category_info.dart';
import '../widgets/common/custom_app_bar.dart';

class JlptLevelScreen extends StatefulWidget {
  final String level;
  final Color? color;

  const JlptLevelScreen({
    super.key,
    required this.level,
    this.color,
  });

  @override
  State<JlptLevelScreen> createState() => _JlptLevelScreenState();
}

class _JlptLevelScreenState extends State<JlptLevelScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _headerAnimationController;
  late AnimationController _cardAnimationController;
  late Animation<double> _headerFadeAnimation;
  late Animation<Offset> _cardSlideAnimation;

  late WordService _wordService;
  late KanjiService _kanjiService;
  late UserProgressService _userProgressService;
  late AudioService _audioService;
  late HapticService _hapticService;

  List<Word> _words = [];
  List<Kanji> _kanji = [];
  bool _isLoading = true;
  int _learnedWordsCount = 0;
  int _learnedKanjiCount = 0;
  int _totalWordsCount = 0;
  int _totalKanjiCount = 0;

  // JLPT seviye renkleri
  Color get _levelColor {
    switch (widget.level) {
      case 'N5': return const Color(0xFF4CAF50);
      case 'N4': return const Color(0xFFFF9800);
      case 'N3': return const Color(0xFFFFC107);
      case 'N2': return const Color(0xFF00BCD4);
      case 'N1': return const Color(0xFF3F51B5);
      default: return const Color(0xFF4CAF50);
    }
  }

  Color get _levelSecondaryColor {
    switch (widget.level) {
      case 'N5': return const Color(0xFF66BB6A);
      case 'N4': return const Color(0xFFFFB74D);
      case 'N3': return const Color(0xFFFFD54F);
      case 'N2': return const Color(0xFF4DD0E1);
      case 'N1': return const Color(0xFF7986CB);
      default: return const Color(0xFF66BB6A);
    }
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeAnimations();
    _wordService = Provider.of<WordService>(context, listen: false);
    _kanjiService = Provider.of<KanjiService>(context, listen: false);
    _userProgressService = Provider.of<UserProgressService>(context, listen: false);
    _audioService = Provider.of<AudioService>(context, listen: false);
    _hapticService = Provider.of<HapticService>(context, listen: false);
    _loadData();
  }

  void _initializeAnimations() {
    _headerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _cardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _headerFadeAnimation = CurvedAnimation(
      parent: _headerAnimationController,
      curve: Curves.easeInOut,
    );

    _cardSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _cardAnimationController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _tabController.dispose();
    _headerAnimationController.dispose();
    _cardAnimationController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      // JLPT seviyesini sayıya çevir (N5=5, N4=4, etc.)
      final int jlptNumber = int.parse(widget.level.substring(1));

      // Kelimeleri yükle
      await _wordService.loadWords();
      final words = _wordService.words.where((w) => w.jlptLevel == jlptNumber).toList();
      final kanji = _kanjiService.getKanjiByJlptLevelWithProgress(jlptNumber);

      // İlerleme verilerini yükle
      final wordIds = words.map((w) => w.id.toString()).toList();
      final kanjiIds = kanji.map((k) => k.kanji).toList();
      
      final learnedWords = await _userProgressService.getLearnedCountForWordIds(wordIds);
      final learnedKanji = await _userProgressService.getLearnedKanjiCount();

      if (mounted) {
        setState(() {
          _words = words;
          _kanji = kanji;
          _totalWordsCount = words.length;
          _totalKanjiCount = kanji.length;
          _learnedWordsCount = learnedWords;
          _learnedKanjiCount = learnedKanji;
          _isLoading = false;
        });

        // Animasyonları başlat
        _headerAnimationController.forward();
        _cardAnimationController.forward();
      }
    } catch (e, s) {
      AppLogger.error('JLPT ${widget.level} verileri yüklenirken hata', e, s);
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : NestedScrollView(
              headerSliverBuilder: (context, innerBoxIsScrolled) {
                return [
                  _buildModernAppBar(),
                ];
              },
              body: TabBarView(
                controller: _tabController,
                children: [
                  _buildWordsTab(),
                  _buildKanjiTab(),
                  _buildEnhancedPracticeTab(),
                ],
              ),
            ),
    );
  }

  Widget _buildModernAppBar() {
    final double wordProgress = _totalWordsCount > 0 ? _learnedWordsCount / _totalWordsCount : 0.0;
    final double kanjiProgress = _totalKanjiCount > 0 ? _learnedKanjiCount / _totalKanjiCount : 0.0;
    final double overallProgress = (wordProgress + kanjiProgress) / 2;

    return SliverAppBar(
      expandedHeight: 280,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: _levelColor,
      foregroundColor: Colors.white,
      flexibleSpace: FlexibleSpaceBar(
        background: FadeTransition(
          opacity: _headerFadeAnimation,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  _levelColor,
                  _levelSecondaryColor,
                ],
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 60, 16, 16),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                    // Level badge ve başlık
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            widget.level,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'JLPT ${widget.level}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 28,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                _getLevelDescription(),
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.9),
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    
                    // Progress cards
                    Row(
                      children: [
                        Expanded(
                          child: _buildModernProgressCard(
                            'Kelimeler',
                            _learnedWordsCount,
                            _totalWordsCount,
                            wordProgress,
                            Icons.book,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildModernProgressCard(
                            'Kanji',
                            _learnedKanjiCount,
                            _totalKanjiCount,
                            kanjiProgress,
                            Icons.translate,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    // Overall progress
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'Genel İlerleme',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                '${(overallProgress * 100).toInt()}%',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          LinearProgressIndicator(
                            value: overallProgress,
                            backgroundColor: Colors.white.withOpacity(0.3),
                            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                            minHeight: 8,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ],
                      ),
                    ),
                  ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(48),
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(25),
          ),
          child: TabBar(
            controller: _tabController,
            indicator: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              color: Colors.white.withOpacity(0.3),
            ),
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            labelStyle: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
            tabs: const [
              Tab(
                icon: Icon(Icons.book, size: 18),
                text: 'Kelimeler',
              ),
              Tab(
                icon: Icon(Icons.translate, size: 18),
                text: 'Kanji',
              ),
              Tab(
                icon: Icon(Icons.quiz, size: 18),
                text: 'Pratik',
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getLevelDescription() {
    switch (widget.level) {
      case 'N5': return 'Başlangıç seviyesi - Temel Japonca';
      case 'N4': return 'Temel-orta seviye - Günlük konuşma';
      case 'N3': return 'Orta seviye - Günlük yaşam';
      case 'N2': return 'İleri seviye - İş ve akademik';
      case 'N1': return 'Uzman seviye - Profesyonel';
      default: return 'Japonca yeterlilik testi';
    }
  }

  Widget _buildModernProgressCard(
    String title,
    int learned,
    int total,
    double progress,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '$learned/$total',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.white.withOpacity(0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
            minHeight: 6,
            borderRadius: BorderRadius.circular(3),
          ),
        ],
      ),
    );
  }

  Widget _buildWordsTab() {
    if (_words.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.book_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Kelime bulunamadı',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return SlideTransition(
      position: _cardSlideAnimation,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _words.length,
        itemBuilder: (context, index) {
          final word = _words[index];
          return TweenAnimationBuilder<double>(
            duration: Duration(milliseconds: 300 + (index * 50)),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.translate(
                offset: Offset(0, 30 * (1 - value)),
                child: Opacity(
                  opacity: value,
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ListTile(
                      contentPadding: const EdgeInsets.all(16),
                      title: Text(
                        word.japanese,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Noto Sans JP',
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (word.romaji.isNotEmpty) ...[
                            const SizedBox(height: 4),
                            Text(
                              word.romaji,
                              style: TextStyle(
                                fontSize: 14,
                                fontStyle: FontStyle.italic,
                                color: _levelColor,
                              ),
                            ),
                          ],
                          const SizedBox(height: 4),
                          Text(
                            word.meaningsTr.join(', '),
                            style: const TextStyle(fontSize: 16),
                          ),
                        ],
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: Icon(
                              Icons.volume_up,
                              color: _levelColor,
                            ),
                            onPressed: () {
                              _audioService.playClickSound();
                              // TTS çal
                            },
                          ),
                          IconButton(
                            icon: Icon(
                              word.isFavorite ? Icons.favorite : Icons.favorite_border,
                              color: word.isFavorite ? Colors.red : Colors.grey,
                            ),
                            onPressed: () {
                              _audioService.playClickSound();
                              _hapticService.lightImpact();
                              // Favori durumunu değiştir
                            },
                          ),
                        ],
                      ),
                      onTap: () {
                        _audioService.playClickSound();
                        _hapticService.lightImpact();
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => WordDetailPage(wordId: word.id),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildKanjiTab() {
    if (_kanji.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.translate_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Kanji bulunamadı',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return SlideTransition(
      position: _cardSlideAnimation,
      child: GridView.builder(
        padding: const EdgeInsets.all(16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 1,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        itemCount: _kanji.length,
        itemBuilder: (context, index) {
          final kanji = _kanji[index];
          return TweenAnimationBuilder<double>(
            duration: Duration(milliseconds: 400 + (index * 50)),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: _levelColor.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                    border: Border.all(
                      color: _levelColor.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(16),
                    child: InkWell(
                      onTap: () {
                        _audioService.playClickSound();
                        _hapticService.lightImpact();
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => KanjiDetailScreen(
                              kanji: kanji,
                            ),
                          ),
                        );
                      },
                      borderRadius: BorderRadius.circular(16),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              _levelColor.withOpacity(0.1),
                              _levelSecondaryColor.withOpacity(0.05),
                            ],
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              kanji.kanji,
                              style: const TextStyle(
                                fontSize: 36,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Noto Sans JP',
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              kanji.meanings?.isNotEmpty == true ? kanji.meanings!.first : '',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildEnhancedPracticeTab() {
    return SlideTransition(
      position: _cardSlideAnimation,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    _levelColor.withOpacity(0.1),
                    _levelSecondaryColor.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: _levelColor.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: _levelColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.fitness_center,
                      color: _levelColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Pratik Egzersizleri',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: _levelColor,
                          ),
                        ),
                        Text(
                          'Öğrendiklerinizi pekiştirin',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            
            // Practice cards
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                childAspectRatio: 1.1,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildEnhancedPracticeCard(
                    'Quiz Pratiği',
                    'Bilginizi test edin',
                    Icons.quiz,
                    const Color(0xFF2196F3),
                    () {
                      _audioService.playClickSound();
                      _hapticService.mediumImpact();
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => QuizScreen(
                            words: _words,
                            quizTuru: 'jlpt_${widget.level}',
                            title: 'JLPT ${widget.level} Quiz',
                          ),
                        ),
                      );
                    },
                  ),
                  _buildEnhancedPracticeCard(
                    'Hız Testi',
                    'Hızlı hatırlama',
                    Icons.speed,
                    const Color(0xFFFF5722),
                    () {
                      _audioService.playClickSound();
                      _hapticService.mediumImpact();
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => SpeedTestScreen(
                            category: 'jlpt_${widget.level}',
                            words: _words,
                          ),
                        ),
                      );
                    },
                  ),
                  _buildEnhancedPracticeCard(
                    'Kanji Tanıma',
                    'Kanji becerilerinizi geliştirin',
                    Icons.translate,
                    const Color(0xFF4CAF50),
                    () {
                      _audioService.playClickSound();
                      _hapticService.mediumImpact();
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => KanjiRecognitionTestScreen(
                            category: CategoryInfo(
                              id: 'kanji_recognition_${widget.level}',
                              name: 'Kanji Recognition',
                              description: 'JLPT ${widget.level} Kanji Tanıma',
                              wordCount: _totalKanjiCount,
                              iconPath: 'assets/images/kanji_recognition.webp',
                              difficulty: widget.level,
                              type: 'jlpt',
                              subType: widget.level,
                              titleKey: 'jlpt_${widget.level}',
                              icon: Icons.translate,
                              primaryColor: _levelColor,
                              secondaryColor: _levelSecondaryColor,
                            ),
                            title: 'JLPT ${widget.level} Kanji Tanıma',
                          ),
                        ),
                      );
                    },
                  ),
                  _buildEnhancedPracticeCard(
                    'Flashcards',
                    'Kart çalışması',
                    Icons.style,
                    const Color(0xFF9C27B0),
                    () {
                      _audioService.playClickSound();
                      _hapticService.mediumImpact();
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const FlashcardScreen(),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
            
            // Progress summary
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.insights,
                    color: _levelColor,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Günlük Hedef',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '${_learnedWordsCount + _learnedKanjiCount} / ${(_totalWordsCount + _totalKanjiCount) * 0.1} öğrenildi',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  CircularProgressIndicator(
                    value: (_learnedWordsCount + _learnedKanjiCount) / ((_totalWordsCount + _totalKanjiCount) * 0.1),
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(_levelColor),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedPracticeCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 600),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * value),
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: color.withOpacity(0.2),
                  blurRadius: 12,
                  offset: const Offset(0, 6),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(20),
              child: InkWell(
                onTap: onTap,
                borderRadius: BorderRadius.circular(20),
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        color.withOpacity(0.1),
                        color.withOpacity(0.05),
                      ],
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: color.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Icon(
                          icon,
                          size: 32,
                          color: color,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
} 
