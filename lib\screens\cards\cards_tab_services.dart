import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/kelime_service.dart';
import '../../services/kanji_service.dart';
import '../../services/level_service.dart';
import '../../services/difficulty_algorithm_service.dart';
import '../../services/enhanced_statistics_service.dart';
import '../../services/enhanced_kanji_learning_service.dart';
import '../../utils/page_transitions.dart';
import '../../models/category_info.dart';
import '../../services/audio_service.dart';
import '../../services/haptic_service.dart';
import '../../models/category_info.dart';
import '../flashcard_screen.dart';
import '../radicals_learning_screen.dart';
import '../kanji_recognition_test_screen.dart';
import '../level_progression_screen.dart';
import '../vocabulary_screen.dart';
import '../enhanced_grammar_screen.dart';
import '../jlpt_level_screen.dart';
import 'package:japoni_go/utils/app_logger.dart';

class CardsTabServices {
  late KelimeService _kelimeService;
  late KanjiService _kanjiService;
  late LevelService _levelService;
  late DifficultyAlgorithmService _difficultyService;
  late EnhancedStatisticsService _statisticsService;
  late EnhancedKanjiLearningService _enhancedKanjiLearningService;
  late AudioService _audioService;
  late HapticService _hapticService;

  bool _servicesInitialized = false;

  bool get servicesInitialized => _servicesInitialized;

  void initializeServices(BuildContext context) {
    try {
      _kelimeService = Provider.of<KelimeService>(context, listen: false);
      _kanjiService = Provider.of<KanjiService>(context, listen: false);
      _levelService = Provider.of<LevelService>(context, listen: false);
      _difficultyService = Provider.of<DifficultyAlgorithmService>(context, listen: false);
      _statisticsService = Provider.of<EnhancedStatisticsService>(context, listen: false);
      _enhancedKanjiLearningService = Provider.of<EnhancedKanjiLearningService>(context, listen: false);
      _audioService = Provider.of<AudioService>(context, listen: false);
      _hapticService = Provider.of<HapticService>(context, listen: false);
      
      _servicesInitialized = true;
      AppLogger.info('CardsTab services initialized successfully');
    } catch (e) {
      AppLogger.error('Error initializing CardsTab services', e);
      _servicesInitialized = false;
    }
  }

  void handleCategoryTap(BuildContext context, CategoryInfo category) {
    if (!_servicesInitialized) {
      AppLogger.warning('Services not initialized, cannot handle category tap');
      return;
    }

    _hapticService.lightImpact();
    _audioService.playButtonClick();

    try {
      switch (category.type) {
        case 'jlpt':
          _navigateToJLPTLevel(context, category);
          break;
        case 'theme':
          _navigateToThematicCategory(context, category);
          break;
        case 'grammar':
          _navigateToGrammarScreen(context, category);
          break;
        case 'kanji':
          _navigateToKanjiScreen(context, category);
          break;
        case 'vocabulary':
          _navigateToVocabularyScreen(context, category);
          break;
        default:
          _navigateToFlashcardScreen(context, category);
      }
    } catch (e) {
      AppLogger.error('Error handling category tap for ${category.id}', e);
      _showErrorSnackBar(context, 'Kategori açılırken hata oluştu');
    }
  }

  void _navigateToJLPTLevel(BuildContext context, CategoryInfo category) {
    Navigator.of(context).push(
      PageTransitions.slideFromRight(
        JlptLevelScreen(
          level: category.subType ?? 'N5',
        ),
      ),
    );
  }

  void _navigateToThematicCategory(BuildContext context, CategoryInfo category) {
    Navigator.of(context).push(
      PageTransitions.slideFromRight(
        VocabularyScreen(
          category: category.id,
          title: category.name,
        ),
      ),
    );
  }

  void _navigateToGrammarScreen(BuildContext context, CategoryInfo category) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const EnhancedGrammarScreen(),
      ),
    );
  }

  void _navigateToKanjiScreen(BuildContext context, CategoryInfo category) {
    Navigator.of(context).push(
      PageTransitions.slideFromRight(
        KanjiRecognitionTestScreen(
          category: category,
          title: category.name,
        ),
      ),
    );
  }

  void _navigateToVocabularyScreen(BuildContext context, CategoryInfo category) {
    Navigator.of(context).push(
      PageTransitions.slideFromRight(
        VocabularyScreen(
          category: category.id,
          title: category.name,
        ),
      ),
    );
  }

  void _navigateToFlashcardScreen(BuildContext context, CategoryInfo category) {
    Navigator.of(context).push(
      PageTransitions.slideFromRight(
        const FlashcardScreen(),
      ),
    );
  }

  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // Getters for services (if needed by other components)
  KelimeService get kelimeService => _kelimeService;
  KanjiService get kanjiService => _kanjiService;
  LevelService get levelService => _levelService;
  DifficultyAlgorithmService get difficultyService => _difficultyService;
  EnhancedStatisticsService get statisticsService => _statisticsService;
  EnhancedKanjiLearningService get enhancedKanjiLearningService => _enhancedKanjiLearningService;
  AudioService get audioService => _audioService;
  HapticService get hapticService => _hapticService;

  void dispose() {
    // Clean up any resources if needed
    _servicesInitialized = false;
  }
}