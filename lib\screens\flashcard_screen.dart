import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/word.dart';
import '../services/word_service.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:japoni_go/utils/app_logger.dart';
import 'cards_tab.dart';
import '../widgets/flashcard_widget.dart';
import '../widgets/alphabet_card_widget.dart';
import '../widgets/grammar_card_widget.dart';
import '../models/grammar_rule.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../screens/home_tab.dart';
import '../services/user_progress_service.dart';
import '../services/stats_service.dart';
import '../models/enums.dart'; // KelimeDurumu enum'u için eklendi
import '../models/category_info.dart';
import '../services/audio_service.dart';
import '../widgets/animated_page_transition.dart';
import '../widgets/quick_feedback_button.dart';
import 'package:flip_card/flip_card.dart';
import 'package:japoni_go/widgets/common/custom_app_bar.dart';
import 'package:japoni_go/widgets/common/loading_indicator.dart';

class FlashcardScreen extends StatefulWidget {
  const FlashcardScreen({Key? key}) : super(key: key);

  @override
  _FlashcardScreenState createState() => _FlashcardScreenState();
}

class _FlashcardScreenState extends State<FlashcardScreen> {
  late WordService _wordService;
  List<Word> _flashcardWords = [];
  bool _isLoading = true;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    // Use addPostFrameCallback to ensure context is available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _wordService = Provider.of<WordService>(context, listen: false);
      _loadFlashcards();
    });
  }

  Future<void> _loadFlashcards() async {
    if (!mounted) return;
    setState(() => _isLoading = true);
    try {
      // Assuming words are already loaded by a central service at app start.
      // If not, we might need to call _wordService.loadWords() here.
      final allWords = await _wordService.getAllWords(pageSize: 50); // Fetch a batch
      allWords.shuffle();
      if (mounted) {
        setState(() {
          _flashcardWords = allWords.take(20).toList(); // Take 20 for a session
          _isLoading = false;
          _currentIndex = 0;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('flashcardLoadError'.tr())),
        );
      }
    }
  }

  void _nextCard() {
    setState(() {
      if (_currentIndex < _flashcardWords.length - 1) {
        _currentIndex++;
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('flashcardsComplete'.tr())),
        );
        _loadFlashcards(); // Restart with new cards
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: 'flashcards'.tr()),
      body: _isLoading
          ? const LoadingIndicator()
          : _flashcardWords.isEmpty
              ? Center(child: Text('noFlashcardsAvailable'.tr()))
              : _buildFlashcardView(),
    );
  }

  Widget _buildFlashcardView() {
    final word = _flashcardWords[_currentIndex];
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        FlipCard(
          front: _buildCardFace(word.japanese, isFront: true),
          back: _buildCardFace(word.meaningsTr.join(', \n')),
        ),
        const SizedBox(height: 30),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: ElevatedButton(
            onPressed: _nextCard,
            child: Text('next'.tr()),
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(double.infinity, 50),
            ),
          ),
        ),
        const SizedBox(height: 20),
        Text('${_currentIndex + 1} / ${_flashcardWords.length}'),
      ],
    );
  }

  Widget _buildCardFace(String text, {bool isFront = false}) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.3,
        alignment: Alignment.center,
        padding: const EdgeInsets.all(16.0),
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: isFront
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurface,
              ),
        ),
      ),
    );
  }
}