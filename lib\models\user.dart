import 'package:equatable/equatable.dart';

/// Kullanıcı modeli
/// Performans odaklı ve modüler yapıda tasarlanmıştır
class User extends Equatable {
  final String id;
  final String email;
  final String? displayName;
  final String? photoUrl;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final bool isEmailVerified;
  final bool isPremium;
  final DateTime? premiumExpiryDate;
  final String? premiumPlan;
  final Map<String, dynamic> preferences;
  final Map<String, dynamic> statistics;
  final int level;
  final int experience;
  final int streak;
  final DateTime? lastStudyDate;

  const User({
    required this.id,
    required this.email,
    this.displayName,
    this.photoUrl,
    required this.createdAt,
    this.lastLoginAt,
    this.isEmailVerified = false,
    this.isPremium = false,
    this.premiumExpiryDate,
    this.premiumPlan,
    this.preferences = const {},
    this.statistics = const {},
    this.level = 1,
    this.experience = 0,
    this.streak = 0,
    this.lastStudyDate,
  });

  /// Kullanıcının premium durumu aktif mi
  bool get isPremiumActive {
    if (!isPremium) return false;
    if (premiumExpiryDate == null) return true; // Lifetime
    return DateTime.now().isBefore(premiumExpiryDate!);
  }

  /// Kullanıcının günlük streak'i devam ediyor mu
  bool get isStreakActive {
    if (lastStudyDate == null) return false;
    final now = DateTime.now();
    final lastStudy = lastStudyDate!;
    final difference = now.difference(lastStudy).inDays;
    return difference <= 1;
  }

  /// Kullanıcının seviyesi için gereken toplam deneyim
  int get experienceForCurrentLevel {
    return level * 1000; // Her seviye için 1000 XP
  }

  /// Bir sonraki seviye için gereken deneyim
  int get experienceForNextLevel {
    return (level + 1) * 1000;
  }

  /// Mevcut seviyedeki ilerleme yüzdesi
  double get levelProgress {
    final currentLevelExp = experienceForCurrentLevel;
    final nextLevelExp = experienceForNextLevel;
    final progressExp = experience - currentLevelExp;
    final totalExpForLevel = nextLevelExp - currentLevelExp;
    
    if (totalExpForLevel <= 0) return 1.0;
    return (progressExp / totalExpForLevel).clamp(0.0, 1.0);
  }

  /// Kullanıcı kopyası oluştur
  User copyWith({
    String? id,
    String? email,
    String? displayName,
    String? photoUrl,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    bool? isEmailVerified,
    bool? isPremium,
    DateTime? premiumExpiryDate,
    String? premiumPlan,
    Map<String, dynamic>? preferences,
    Map<String, dynamic>? statistics,
    int? level,
    int? experience,
    int? streak,
    DateTime? lastStudyDate,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoUrl: photoUrl ?? this.photoUrl,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPremium: isPremium ?? this.isPremium,
      premiumExpiryDate: premiumExpiryDate ?? this.premiumExpiryDate,
      premiumPlan: premiumPlan ?? this.premiumPlan,
      preferences: preferences ?? this.preferences,
      statistics: statistics ?? this.statistics,
      level: level ?? this.level,
      experience: experience ?? this.experience,
      streak: streak ?? this.streak,
      lastStudyDate: lastStudyDate ?? this.lastStudyDate,
    );
  }

  /// JSON'dan kullanıcı oluştur
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      email: json['email'] as String,
      displayName: json['display_name'] as String?,
      photoUrl: json['photo_url'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastLoginAt: json['last_login_at'] != null
          ? DateTime.parse(json['last_login_at'] as String)
          : null,
      isEmailVerified: json['is_email_verified'] as bool? ?? false,
      isPremium: json['is_premium'] as bool? ?? false,
      premiumExpiryDate: json['premium_expiry_date'] != null
          ? DateTime.parse(json['premium_expiry_date'] as String)
          : null,
      premiumPlan: json['premium_plan'] as String?,
      preferences: Map<String, dynamic>.from(json['preferences'] ?? {}),
      statistics: Map<String, dynamic>.from(json['statistics'] ?? {}),
      level: json['level'] as int? ?? 1,
      experience: json['experience'] as int? ?? 0,
      streak: json['streak'] as int? ?? 0,
      lastStudyDate: json['last_study_date'] != null
          ? DateTime.parse(json['last_study_date'] as String)
          : null,
    );
  }

  /// JSON'a dönüştür
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'display_name': displayName,
      'photo_url': photoUrl,
      'created_at': createdAt.toIso8601String(),
      'last_login_at': lastLoginAt?.toIso8601String(),
      'is_email_verified': isEmailVerified,
      'is_premium': isPremium,
      'premium_expiry_date': premiumExpiryDate?.toIso8601String(),
      'premium_plan': premiumPlan,
      'preferences': preferences,
      'statistics': statistics,
      'level': level,
      'experience': experience,
      'streak': streak,
      'last_study_date': lastStudyDate?.toIso8601String(),
    };
  }

  /// Veritabanı map'inden kullanıcı oluştur
  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'] as String,
      email: map['email'] as String,
      displayName: map['display_name'] as String?,
      photoUrl: map['photo_url'] as String?,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      lastLoginAt: map['last_login_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['last_login_at'] as int)
          : null,
      isEmailVerified: (map['is_email_verified'] as int?) == 1,
      isPremium: (map['is_premium'] as int?) == 1,
      premiumExpiryDate: map['premium_expiry_date'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['premium_expiry_date'] as int)
          : null,
      premiumPlan: map['premium_plan'] as String?,
      preferences: map['preferences'] != null
          ? Map<String, dynamic>.from(map['preferences'] as Map)
          : {},
      statistics: map['statistics'] != null
          ? Map<String, dynamic>.from(map['statistics'] as Map)
          : {},
      level: map['level'] as int? ?? 1,
      experience: map['experience'] as int? ?? 0,
      streak: map['streak'] as int? ?? 0,
      lastStudyDate: map['last_study_date'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['last_study_date'] as int)
          : null,
    );
  }

  /// Veritabanı map'ine dönüştür
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'display_name': displayName,
      'photo_url': photoUrl,
      'created_at': createdAt.millisecondsSinceEpoch,
      'last_login_at': lastLoginAt?.millisecondsSinceEpoch,
      'is_email_verified': isEmailVerified ? 1 : 0,
      'is_premium': isPremium ? 1 : 0,
      'premium_expiry_date': premiumExpiryDate?.millisecondsSinceEpoch,
      'premium_plan': premiumPlan,
      'preferences': preferences,
      'statistics': statistics,
      'level': level,
      'experience': experience,
      'streak': streak,
      'last_study_date': lastStudyDate?.millisecondsSinceEpoch,
    };
  }

  @override
  List<Object?> get props => [
        id,
        email,
        displayName,
        photoUrl,
        createdAt,
        lastLoginAt,
        isEmailVerified,
        isPremium,
        premiumExpiryDate,
        premiumPlan,
        preferences,
        statistics,
        level,
        experience,
        streak,
        lastStudyDate,
      ];

  @override
  String toString() {
    return 'User(id: $id, email: $email, displayName: $displayName, level: $level, experience: $experience, isPremium: $isPremium)';
  }
}

/// Kullanıcı tercihleri için sabitler
class UserPreferences {
  static const String language = 'language';
  static const String theme = 'theme';
  static const String notifications = 'notifications';
  static const String soundEnabled = 'sound_enabled';
  static const String vibrationEnabled = 'vibration_enabled';
  static const String dailyGoal = 'daily_goal';
  static const String reminderTime = 'reminder_time';
  static const String autoPlay = 'auto_play';
  static const String showRomaji = 'show_romaji';
  static const String showFurigana = 'show_furigana';
}

/// Kullanıcı istatistikleri için sabitler
class UserStatistics {
  static const String totalStudyTime = 'total_study_time';
  static const String wordsLearned = 'words_learned';
  static const String kanjiLearned = 'kanji_learned';
  static const String quizzesCompleted = 'quizzes_completed';
  static const String gamesPlayed = 'games_played';
  static const String correctAnswers = 'correct_answers';
  static const String totalAnswers = 'total_answers';
  static const String longestStreak = 'longest_streak';
  static const String studyDays = 'study_days';
  static const String averageSessionTime = 'average_session_time';
}
