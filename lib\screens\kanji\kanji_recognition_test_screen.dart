import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../models/kanji.dart';
import '../../widgets/common/loading_indicator.dart';

/// Kanji tanıma testi ekranı
/// Performans odaklı ve modüler yapıda tasarlanmıştır
class KanjiRecognitionTestScreen extends StatefulWidget {
  final List<Kanji> kanjiList;
  final VoidCallback? onTestComplete;

  const KanjiRecognitionTestScreen({
    super.key,
    required this.kanjiList,
    this.onTestComplete,
  });

  @override
  State<KanjiRecognitionTestScreen> createState() => _KanjiRecognitionTestScreenState();
}

class _KanjiRecognitionTestScreenState extends State<KanjiRecognitionTestScreen>
    with TickerProviderStateMixin {
  late List<KanjiQuestion> _questions;
  late AnimationController _progressController;
  late AnimationController _feedbackController;
  
  int _currentQuestionIndex = 0;
  int _score = 0;
  int _correctAnswers = 0;
  bool _isAnswered = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeTest();
    _setupAnimations();
  }

  void _initializeTest() {
    _questions = [];
    final testKanji = widget.kanjiList.take(15).toList(); // 15 kanji testi

    for (int i = 0; i < testKanji.length; i++) {
      final kanji = testKanji[i];
      final correctAnswer = kanji.meanings?.isNotEmpty == true
          ? kanji.meanings!.first
          : kanji.character;

      // Yanlış cevaplar için diğer kanji'lerden seç
      final wrongAnswers = <String>[];
      final otherKanji = testKanji.where((k) => k != kanji).toList();
      otherKanji.shuffle();
      
      for (int j = 0; j < 3 && j < otherKanji.length; j++) {
        final wrongAnswer = otherKanji[j].meanings?.isNotEmpty == true
            ? otherKanji[j].meanings!.first
            : otherKanji[j].character;
        wrongAnswers.add(wrongAnswer);
      }

      final allAnswers = [correctAnswer, ...wrongAnswers];
      allAnswers.shuffle();

      _questions.add(KanjiQuestion(
        kanji: kanji,
        answers: allAnswers,
        correctAnswerIndex: allAnswers.indexOf(correctAnswer),
      ));
    }
  }

  void _setupAnimations() {
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _feedbackController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _progressController.dispose();
    _feedbackController.dispose();
    super.dispose();
  }

  void _onAnswerSelected(int selectedIndex) {
    if (_isAnswered) return;

    setState(() {
      _isAnswered = true;
    });

    final question = _questions[_currentQuestionIndex];
    final isCorrect = selectedIndex == question.correctAnswerIndex;

    if (isCorrect) {
      _correctAnswers++;
      _score += 10;
    }

    question.selectedAnswerIndex = selectedIndex;
    question.isCorrect = isCorrect;

    _feedbackController.forward();

    Future.delayed(const Duration(milliseconds: 1500), () {
      _nextQuestion();
    });
  }

  void _nextQuestion() {
    if (_currentQuestionIndex < _questions.length - 1) {
      setState(() {
        _currentQuestionIndex++;
        _isAnswered = false;
      });
      
      _feedbackController.reverse();
      _progressController.animateTo(
        (_currentQuestionIndex + 1) / _questions.length,
      );
    } else {
      _showTestCompleteDialog();
    }
  }

  void _showTestCompleteDialog() {
    final percentage = (_correctAnswers / _questions.length * 100).round();
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text('test_complete'.tr()),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              percentage >= 80 ? Icons.celebration : Icons.thumb_up,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text('final_score'.tr(args: [_score.toString()])),
            Text('correct_answers'.tr(args: [_correctAnswers.toString(), _questions.length.toString()])),
            Text('percentage'.tr(args: [percentage.toString()])),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onTestComplete?.call();
            },
            child: Text('continue'.tr()),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _resetTest();
            },
            child: Text('try_again'.tr()),
          ),
        ],
      ),
    );
  }

  void _resetTest() {
    setState(() {
      _currentQuestionIndex = 0;
      _score = 0;
      _correctAnswers = 0;
      _isAnswered = false;
      _initializeTest();
    });
    _progressController.reset();
    _feedbackController.reset();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('kanji_recognition_test'.tr()),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(4.0),
          child: AnimatedBuilder(
            animation: _progressController,
            builder: (context, child) {
              return LinearProgressIndicator(
                value: (_currentQuestionIndex + 1) / _questions.length,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              );
            },
          ),
        ),
      ),
      body: _isLoading
          ? const LoadingIndicator()
          : _buildTestContent(),
    );
  }

  Widget _buildTestContent() {
    final question = _questions[_currentQuestionIndex];
    
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildQuestionHeader(),
          const SizedBox(height: 24),
          _buildKanjiCard(question),
          const SizedBox(height: 24),
          Expanded(
            child: _buildAnswerOptions(question),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'question'.tr(args: [(_currentQuestionIndex + 1).toString(), _questions.length.toString()]),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Text(
              'score'.tr(args: [_score.toString()]),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKanjiCard(KanjiQuestion question) {
    return Card(
      elevation: 4,
      child: Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primary.withOpacity(0.1),
              Theme.of(context).colorScheme.secondary.withOpacity(0.05),
            ],
          ),
        ),
        child: Column(
          children: [
            Text(
              'what_does_this_kanji_mean'.tr(),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 24),
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  question.kanji.character,
                  style: Theme.of(context).textTheme.displayLarge?.copyWith(
                    fontSize: 72,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            if (question.kanji.readings.isNotEmpty)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.secondary.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  question.kanji.readings.join(', '),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontStyle: FontStyle.italic,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnswerOptions(KanjiQuestion question) {
    return ListView.builder(
      itemCount: question.answers.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: _buildAnswerOption(question, index),
        );
      },
    );
  }

  Widget _buildAnswerOption(KanjiQuestion question, int index) {
    final isSelected = question.selectedAnswerIndex == index;
    final isCorrect = index == question.correctAnswerIndex;
    
    Color? backgroundColor;
    Color? textColor;
    
    if (_isAnswered) {
      if (isCorrect) {
        backgroundColor = Colors.green.withOpacity(0.3);
        textColor = Colors.green[800];
      } else if (isSelected) {
        backgroundColor = Colors.red.withOpacity(0.3);
        textColor = Colors.red[800];
      }
    }

    return AnimatedBuilder(
      animation: _feedbackController,
      builder: (context, child) {
        return Transform.scale(
          scale: isSelected && _isAnswered ? 1.0 + (_feedbackController.value * 0.05) : 1.0,
          child: Card(
            elevation: isSelected ? 4 : 2,
            color: backgroundColor,
            child: InkWell(
              onTap: () => _onAnswerSelected(index),
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: textColor ?? Theme.of(context).colorScheme.primary,
                      ),
                      child: Center(
                        child: Text(
                          String.fromCharCode(65 + index), // A, B, C, D
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        question.answers[index],
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: textColor,
                          fontWeight: isSelected ? FontWeight.bold : null,
                        ),
                      ),
                    ),
                    if (_isAnswered && isCorrect)
                      Icon(
                        Icons.check_circle,
                        color: Colors.green,
                      )
                    else if (_isAnswered && isSelected && !isCorrect)
                      Icon(
                        Icons.cancel,
                        color: Colors.red,
                      ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Kanji test sorusu modeli
class KanjiQuestion {
  final Kanji kanji;
  final List<String> answers;
  final int correctAnswerIndex;
  int? selectedAnswerIndex;
  bool? isCorrect;

  KanjiQuestion({
    required this.kanji,
    required this.answers,
    required this.correctAnswerIndex,
    this.selectedAnswerIndex,
    this.isCorrect,
  });
}
