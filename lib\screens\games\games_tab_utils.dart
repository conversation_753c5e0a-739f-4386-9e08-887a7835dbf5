import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:japoni_go/models/word.dart';
import 'package:japoni_go/models/category_info.dart';
import 'package:japoni_go/screens/games/word_matching_game.dart';
import 'package:japoni_go/screens/games/memory_game.dart';
import 'package:japoni_go/screens/games/daily_challenge_screen.dart';
import 'package:japoni_go/screens/games/weekly_tasks_screen.dart';
import 'package:japoni_go/screens/speed_test_screen.dart';
import 'package:japoni_go/screens/quiz_screen.dart';
import 'package:japoni_go/screens/kanji/kanji_recognition_test_screen.dart';
import 'package:japoni_go/screens/premium/premium_page.dart';
import 'package:japoni_go/services/premium/premium_service.dart';
import 'package:japoni_go/utils/app_logger.dart';
import 'games_tab_services.dart';

/// Game types enum
enum GameType {
  wordMatching,
  memory,
  dailyChallenge,
  weeklyTasks,
  speedTest,
  quiz,
  kanjiRecognition,
}

/// Games tab utilities - handles game navigation and logic
class GamesTabUtils {
  final GamesTabServices _services = GamesTabServices();

  /// Start a game with playability checks
  Future<int?> startGame({
    required BuildContext context,
    required String gameKey,
    required bool isPremium,
    required List<Word> gameWords,
    required GameType gameType,
  }) async {
    try {
      // Check if game can be played
      final playStatus = await _services.checkGamePlayability(gameKey, isPremium);

      if (!context.mounted) return null;

      // Handle different playability statuses
      switch (playStatus) {
        case PlayabilityStatus.cannotPlay:
          _showPremiumLimitMessage(context);
          return null;

        case PlayabilityStatus.canPlayButDontRecordScore:
          _showPremiumReplayMessage(context);
          break;

        case PlayabilityStatus.canPlayAndRecordScore:
          // Continue to game
          break;
      }

      if (!context.mounted) return null;

      // Navigate to appropriate game
      final result = await _navigateToGame(
        context: context,
        gameType: gameType,
        gameWords: gameWords,
      );

      // Update play status if score should be recorded
      if (result != null && playStatus == PlayabilityStatus.canPlayAndRecordScore) {
        await _services.updatePlayStatus(gameKey, true);
      } else if (result != null) {
        await _services.updatePlayStatus(gameKey, false);
      }

      return result;

    } catch (e, s) {
      AppLogger.error('Error starting game $gameKey', e, s);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.tr('error_starting_game')))
        );
      }
      return null;
    }
  }

  /// Navigate to specific game based on type
  Future<int?> _navigateToGame({
    required BuildContext context,
    required GameType gameType,
    required List<Word> gameWords,
  }) async {
    try {
      switch (gameType) {
        case GameType.wordMatching:
          return await Navigator.of(context).push<int>(
            MaterialPageRoute(
              builder: (context) => WordMatchingGame(words: gameWords),
            ),
          );

        case GameType.memory:
          return await Navigator.of(context).push<int>(
            MaterialPageRoute(
              builder: (context) => MemoryGame(words: gameWords),
            ),
          );

        case GameType.dailyChallenge:
          return await Navigator.of(context).push<int>(
            MaterialPageRoute(
              builder: (context) => DailyChallengeScreen(words: gameWords),
            ),
          );

        case GameType.weeklyTasks:
          return await Navigator.of(context).push<int>(
            MaterialPageRoute(
              builder: (context) => WeeklyTasksScreen(),
            ),
          );

        case GameType.speedTest:
          return await Navigator.of(context).push<int>(
            MaterialPageRoute(
              builder: (context) => SpeedTestScreen(category: 'general'),
            ),
          );

        case GameType.quiz:
          return await Navigator.of(context).push<int>(
            MaterialPageRoute(
              builder: (context) => QuizScreen(
                words: gameWords,
                quizTuru: 'game_quiz',
                mode: QuizMode.wordToMeaning,
              ),
            ),
          );

        case GameType.kanjiRecognition:
          return await Navigator.of(context).push<int>(
            MaterialPageRoute(
              builder: (context) => KanjiRecognitionTestScreen(
                kanjiList: const [], // Empty list for now
                onTestComplete: () {
                  Navigator.of(context).pop();
                },
              ),
            ),
          );

        default:
          AppLogger.warning('Unknown game type: $gameType');
          return null;
      }
    } catch (e, s) {
      AppLogger.error('Error navigating to game $gameType', e, s);
      return null;
    }
  }

  /// Show premium limit message
  void _showPremiumLimitMessage(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.tr('gameFreeLimitReached')),
        duration: const Duration(seconds: 4),
        backgroundColor: Colors.orange,
        action: SnackBarAction(
          label: context.tr('upgrade'),
          textColor: Colors.white,
          onPressed: () {
            Navigator.pushNamed(context, '/premium');
          },
        ),
      ),
    );
  }

  /// Show premium replay message
  void _showPremiumReplayMessage(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(context.tr('gamePremiumReplayScoreInfo')),
        duration: const Duration(seconds: 5),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// Static helper method for quick navigation to games
  static Future<int?> navigateToGame(BuildContext context, GameType gameType) async {
    final utils = GamesTabUtils();
    final services = GamesTabServices();
    
    try {
      // Initialize services
      await services.initialize(context);
      
      // Get basic game words
      final gameWords = await services.getGameWords(gameType.toString(), limit: 20);
      
      // Check premium status
      final isPremium = await services.checkPremiumStatus();
      
      // Start the game
      return await utils.startGame(
        context: context,
        gameKey: gameType.toString(),
        isPremium: isPremium,
        gameWords: gameWords,
        gameType: gameType,
      );
    } catch (e, s) {
      AppLogger.error('Error in navigateToGame', e, s);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Oyun başlatılırken hata oluştu: ${e.toString()}')),
        );
      }
      return null;
    }
  }

  /// Get game icon based on type
  IconData getGameIcon(GameType gameType) {
    switch (gameType) {
      case GameType.wordMatching:
        return Icons.link;
      case GameType.memory:
        return Icons.memory;
      case GameType.dailyChallenge:
        return Icons.today;
      case GameType.weeklyTasks:
        return Icons.assignment;
      case GameType.speedTest:
        return Icons.speed;
      case GameType.quiz:
        return Icons.quiz;
      case GameType.kanjiRecognition:
        return Icons.draw;
      default:
        return Icons.games;
    }
  }

  /// Get game color based on type
  Color getGameColor(GameType gameType) {
    switch (gameType) {
      case GameType.wordMatching:
        return Colors.blue;
      case GameType.memory:
        return Colors.purple;
      case GameType.dailyChallenge:
        return Colors.orange;
      case GameType.weeklyTasks:
        return Colors.green;
      case GameType.speedTest:
        return Colors.red;
      case GameType.quiz:
        return Colors.indigo;
      case GameType.kanjiRecognition:
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  /// Get game title key for localization
  String getGameTitleKey(GameType gameType) {
    switch (gameType) {
      case GameType.wordMatching:
        return 'gameTitleWordMatching';
      case GameType.memory:
        return 'gameTitleMemory';
      case GameType.dailyChallenge:
        return 'gameTitleDailyChallenge';
      case GameType.weeklyTasks:
        return 'gameTitleWeeklyTasks';
      case GameType.speedTest:
        return 'gameTitleSpeedTest';
      case GameType.quiz:
        return 'gameTitleQuiz';
      case GameType.kanjiRecognition:
        return 'gameTitleKanjiRecognition';
      default:
        return 'game';
    }
  }

  /// Get game description key for localization
  String getGameDescriptionKey(GameType gameType) {
    switch (gameType) {
      case GameType.wordMatching:
        return 'gameDescWordMatching';
      case GameType.memory:
        return 'gameDescMemory';
      case GameType.dailyChallenge:
        return 'gameDescDailyChallenge';
      case GameType.weeklyTasks:
        return 'gameDescWeeklyTasks';
      case GameType.speedTest:
        return 'gameDescSpeedTest';
      case GameType.quiz:
        return 'gameDescQuiz';
      case GameType.kanjiRecognition:
        return 'gameDescKanjiRecognition';
      default:
        return 'gameDescription';
    }
  }

  /// Check if game is available for free users
  bool isGameFreeForToday(String gameKey, bool isPremium) {
    // Premium users can always play
    if (isPremium) return true;
    
    // For free users, check if they've played today
    // This would typically check SharedPreferences
    // For now, return true (implement actual logic as needed)
    return true;
  }

  /// Get recommended difficulty for user
  String getRecommendedDifficulty(dynamic userData) {
    if (userData == null) return 'N5';
    
    // Implement logic based on user progress
    final level = userData['level'] ?? 1;
    
    if (level >= 50) return 'N1';
    if (level >= 40) return 'N2';
    if (level >= 30) return 'N3';
    if (level >= 20) return 'N4';
    return 'N5';
  }

  /// Format game duration
  String formatGameDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    
    if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  /// Calculate game score multiplier based on difficulty
  double getScoreMultiplier(String difficulty) {
    switch (difficulty.toUpperCase()) {
      case 'N1':
        return 2.0;
      case 'N2':
        return 1.8;
      case 'N3':
        return 1.5;
      case 'N4':
        return 1.2;
      case 'N5':
        return 1.0;
      default:
        return 1.0;
    }
  }
}
