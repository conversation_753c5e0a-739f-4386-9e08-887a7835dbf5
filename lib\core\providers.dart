import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../config/app_theme_configuration.dart';
import '../config/theme_config.dart';
import '../services/database_service.dart';
import '../services/database/search_history_database_service.dart';
import '../services/database/word_database_service.dart';
import '../services/database/database_core.dart';
import '../services/settings_service.dart';
import '../providers/dictionary_state_provider.dart';
import '../providers/all_words_state_provider.dart';
import '../providers/favorites_state_provider.dart';

Future<List<SingleChildWidget>> createProviders() async {
  final prefs = await SharedPreferences.getInstance();
  final databaseService = DatabaseService();
  final databaseCore = DatabaseCore();

  final settingsService = SettingsService();

  return [
    // --- Global Services ---
    Provider<SharedPreferences>.value(value: prefs),
    Provider<DatabaseService>.value(value: databaseService),
    Provider<DatabaseCore>.value(value: databaseCore),
    ChangeNotifierProvider<SettingsService>.value(value: settingsService),

    // --- Dependent Services ---
    Provider<WordDatabaseService>(
      create: (context) => WordDatabaseService(),
    ),
    Provider<SearchHistoryDatabaseService>(
      create: (context) => SearchHistoryDatabaseService(context.read<DatabaseCore>()),
    ),

    // --- UI/Theme Notifiers ---
    Provider<AppThemeConfiguration>(
      create: (context) => AppThemeConfiguration(),
    ),

    // --- State Providers for specific screens ---
    ChangeNotifierProvider<DictionaryStateProvider>(
      create: (context) => DictionaryStateProvider(),
    ),
    ChangeNotifierProvider<AllWordsStateProvider>(
      create: (context) => AllWordsStateProvider(),
    ),
    ChangeNotifierProvider<FavoritesStateProvider>(
      create: (context) => FavoritesStateProvider(
        context.read<WordDatabaseService>(),
      ),
    ),
  ];
} 