import 'dart:async';

import 'package:flutter/foundation.dart';
import '../models/word.dart';
import 'package:japoni_go/utils/app_logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import 'database/analytics_database_service.dart';
import 'database/data_management_service.dart';
import 'kanji_database_service.dart';
import 'database/database_initialization_service.dart';
import 'database/database_performance_service.dart';
import 'database/dictionary_loading_service.dart';
import 'database/kanji_database_service.dart';
import 'database/mobile_search_database_service.dart';
import 'database/sentence_database_service.dart';
import 'database/table_creation_service.dart';
import 'database/user_data_database_service.dart';
import 'database/user_list_database_service.dart';
import 'database/word_database_service.dart';
import 'database/database_core.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  static Database? _database;

  final DatabaseCore _databaseCore = DatabaseCore();
  late final WordDatabaseService wordDbService;
  late final KanjiDatabaseService kanjiDbService;
  late final SentenceDatabaseService sentenceDbService;
  late final UserListDatabaseService userListDbService;
  late final UserDataDatabaseService userDataDbService;
  late final DatabaseInitializationService _initializationService;
  late final TableCreationService _tableCreationService;
  late final AnalyticsDatabaseService analyticsDbService;
  late final DictionaryLoadingService dictionaryLoadingService;
  late final MobileSearchDatabaseService mobileSearchDbService;
  late final DataManagementService dataManagementService;

  // Singleton getter
  static DatabaseService get instance => _instance;

  // Initialization flag
  bool _initialized = false;

  // Performance service instance
  final DatabasePerformanceService _performanceService = DatabasePerformanceService();

  // Cache variables for performance
  final Map<String, dynamic> _queryCache = {};
  
  // Word related operations
  Future<List<Word>> searchWords(String query, {int page = 1, int pageSize = 20}) async {
    try {
      return await wordDbService.searchWords(query, page: page, pageSize: pageSize);
    } catch (e, s) {
      AppLogger.error('Error searching words', e, s);
      return [];
    }
  }
  
  Future<void> updateWordFavoriteStatus(int id, bool isFavorite) async {
    try {
      await wordDbService.updateWordFavoriteStatus(id, isFavorite);
    } catch (e, s) {
      AppLogger.error('Error updating word favorite status', e, s);
      rethrow;
    }
  }
  
  Future<Word?> getWordById(int id) async {
    try {
      return await wordDbService.getWordById(id);
    } catch (e, s) {
      AppLogger.error('Error getting word by ID', e, s);
      return null;
    }
  }

  Future<Word?> getWordOfTheDay() async {
    try {
      return await wordDbService.getWordOfTheDay();
    } catch (e, s) {
      AppLogger.error('Error getting word of the day', e, s);
      return null;
    }
  }

  Future<List<Word>> getAllWords({int page = 1, int pageSize = 50}) async {
    try {
      return await wordDbService.getAllWords(page: page, pageSize: pageSize);
    } catch (e, s) {
      AppLogger.error('Error getting all words', e, s);
      return [];
    }
  }
  
  Future<List<Word>> getFavoriteWords() async {
    try {
      final db = await _databaseCore.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'words',
        where: 'is_favorite = ?',
        whereArgs: [1],
      );
      
      return maps.map((map) => Word.fromMap(map)).toList();
    } catch (e, s) {
      AppLogger.error('Error getting favorite words', e, s);
      return [];
    }
  }
  
  Future<List<Word>> getWordsForPronunciationPractice({int limit = 20}) async {
    try {
      final db = await _databaseCore.database;
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT * FROM words 
        WHERE is_active = 1 
        ORDER BY RANDOM() 
        LIMIT ?
      ''', [limit]);
      
      return maps.map((map) => Word.fromMap(map)).toList();
    } catch (e, s) {
      AppLogger.error('Error getting words for pronunciation practice', e, s);
      return [];
    }
  }

  int _cacheHits = 0;
  int _cacheMisses = 0;

  DatabaseService._internal() {
    wordDbService = WordDatabaseService();
    kanjiDbService = KanjiDatabaseService(this);
    sentenceDbService = SentenceDatabaseService(this);
    userListDbService = UserListDatabaseService(this);
    userDataDbService = UserDataDatabaseService(this);
    _tableCreationService = TableCreationService(
      wordDbService,
      kanjiDbService,
      sentenceDbService,
      userListDbService,
      userDataDbService,
    );
    _initializationService = DatabaseInitializationService(_tableCreationService);
    analyticsDbService = AnalyticsDatabaseService(this);
    dictionaryLoadingService = DictionaryLoadingService(this);
    mobileSearchDbService = MobileSearchDatabaseService(this);
    dataManagementService = DataManagementService(this);
  }

  // Initialize metodu
  Future<void> initialize() async {
    if (_initialized) return;
    await _databaseCore.initialize();
    _initialized = true;
    AppLogger.info('✅ DatabaseService initialized');
  }

  Future<Database> get database async {
    await _databaseCore.initialize();
    return _databaseCore.database;
  }

  void dispose() {
    _database?.close();
    _database = null;
  }

  // ============ EKSİK CRUD METODLARI ============

  // ============ FAVORİ METODLARI ============

  /// Favorilere kelime ekle
  Future<void> addToFavorites(String wordId) async {
    try {
      final db = await database;
      await db.insert(
        'user_favorites',
        {
          'word_id': wordId,
          'created_at': DateTime.now().toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      AppLogger.info('Added to favorites: $wordId');
    } catch (e, s) {
      AppLogger.error('Add to favorites error', e, s);
    }
  }

  /// Favorilerden kelime kaldır
  Future<void> removeFromFavorites(String wordId) async {
    try {
      final db = await database;
      await db.delete(
        'user_favorites',
        where: 'word_id = ?',
        whereArgs: [wordId],
      );
      AppLogger.info('Removed from favorites: $wordId');
    } catch (e, s) {
      AppLogger.error('Remove from favorites error', e, s);
    }
  }

  /// Kullanıcının favori kelime ID'lerini getir
  Future<List<String>> getFavoriteWordIds(String userId) async {
    try {
      final db = await database;
      final result = await db.query(
        'user_favorites',
        columns: ['word_id'],
        where: 'user_id = ?',
        whereArgs: [userId],
      );
      return result.map((row) => row['word_id'] as String).toList();
    } catch (e, s) {
      AppLogger.error('Get favorite word IDs error', e, s);
      return [];
    }
  }

  /// Tüm favorileri temizle
  Future<void> clearFavorites(String userId) async {
    try {
      final db = await database;
      await db.delete(
        'user_favorites',
        where: 'user_id = ?',
        whereArgs: [userId],
      );
      AppLogger.info('Cleared all favorites for user: $userId');
    } catch (e, s) {
      AppLogger.error('Clear favorites error', e, s);
    }
  }

  // ============ ARAMA GEÇMİŞİ METODLARI ============

  /// Arama geçmişine kaydet
  Future<void> saveSearchHistory(Map<String, dynamic> historyItem) async {
    try {
      final db = await database;
      await db.insert(
        'search_history',
        {
          'query': historyItem['query'],
          'result_count': historyItem['resultCount'],
          'timestamp': DateTime.now().toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      AppLogger.info('Saved search history: ${historyItem['query']}');
    } catch (e, s) {
      AppLogger.error('Save search history error', e, s);
    }
  }

  /// Arama geçmişini temizle
  Future<void> clearSearchHistory() async {
    try {
      final db = await database;
      await db.delete('search_history');
      AppLogger.info('Cleared search history');
    } catch (e, s) {
      AppLogger.error('Clear search history error', e, s);
    }
  }

  /// Arama geçmişinden öğe kaldır
  Future<void> removeSearchHistoryItem(Map<String, dynamic> item) async {
    try {
      final db = await database;
      await db.delete(
        'search_history',
        where: 'query = ? AND timestamp = ?',
        whereArgs: [item['query'], item['timestamp']],
      );
      AppLogger.info('Removed search history item: ${item['query']}');
    } catch (e, s) {
      AppLogger.error('Remove search history item error', e, s);
    }
  }
  
  Future<List<Map<String, dynamic>>> getAllKanji() async {
    try {
      final db = await database;
      return await db.query('kanji_characters');
    } catch (e, s) {
      AppLogger.error('Get all kanji error', e, s);
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> getAllVocabulary() async {
    try {
      final db = await database;
      return await db.query('vocabulary_words');
    } catch (e, s) {
      AppLogger.error('Get all vocabulary error', e, s);
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> getAllGrammar() async {
    try {
      final db = await database;
      return await db.query('grammar_rules');
    } catch (e, s) {
      AppLogger.error('Get all grammar error', e, s);
      return [];
    }
  }

  Future<void> insertKanji(Map<String, dynamic> kanji) async {
    try {
      final db = await database;
      await db.insert('kanji_characters', kanji, conflictAlgorithm: ConflictAlgorithm.replace);
    } catch (e, s) {
      AppLogger.error('Insert kanji error', e, s);
    }
  }

  Future<void> updateKanji(String character, Map<String, dynamic> data) async {
    try {
      final db = await database;
      await db.update(
        'kanji_characters',
        data,
        where: 'character = ?',
        whereArgs: [character],
      );
    } catch (e, s) {
      AppLogger.error('Update kanji error', e, s);
    }
  }

  Future<void> deleteKanji(String character) async {
    try {
      final db = await database;
      await db.delete(
        'kanji_characters',
        where: 'character = ?',
        whereArgs: [character],
      );
    } catch (e, s) {
      AppLogger.error('Delete kanji error', e, s);
    }
  }

  Future<void> insertVocabulary(Map<String, dynamic> vocabulary) async {
    try {
      final db = await database;
      await db.insert('vocabulary_words', vocabulary, conflictAlgorithm: ConflictAlgorithm.replace);
    } catch (e, s) {
      AppLogger.error('Insert vocabulary error', e, s);
    }
  }

  Future<void> updateVocabulary(String word, Map<String, dynamic> data) async {
    try {
      final db = await database;
      await db.update(
        'vocabulary_words',
        data,
        where: 'word = ?',
        whereArgs: [word],
      );
    } catch (e, s) {
      AppLogger.error('Update vocabulary error', e, s);
    }
  }

  Future<void> deleteVocabulary(String word) async {
    try {
      final db = await database;
      await db.delete(
        'vocabulary_words',
        where: 'word = ?',
        whereArgs: [word],
      );
    } catch (e, s) {
      AppLogger.error('Delete vocabulary error', e, s);
    }
  }

  Future<void> insertGrammar(Map<String, dynamic> grammar) async {
    try {
      final db = await database;
      await db.insert('grammar_rules', grammar, conflictAlgorithm: ConflictAlgorithm.replace);
    } catch (e, s) {
      AppLogger.error('Insert grammar error', e, s);
    }
  }

  Future<void> updateGrammar(String ruleId, Map<String, dynamic> data) async {
    try {
      final db = await database;
      await db.update(
        'grammar_rules',
        data,
        where: 'rule_id = ?',
        whereArgs: [ruleId],
      );
    } catch (e, s) {
      AppLogger.error('Update grammar error', e, s);
    }
  }

  Future<void> deleteGrammar(String ruleId) async {
    try {
      final db = await database;
      await db.delete(
        'grammar_rules',
        where: 'rule_id = ?',
        whereArgs: [ruleId],
      );
    } catch (e, s) {
      AppLogger.error('Delete grammar error', e, s);
    }
  }

  Future<Map<String, dynamic>?> getKanjiById(String character) async {
    try {
      final db = await database;
      final result = await db.query(
        'kanji_characters',
        where: 'character = ?',
        whereArgs: [character],
        limit: 1,
      );
      return result.isNotEmpty ? result.first : null;
    } catch (e, s) {
      AppLogger.error('Get kanji by id error', e, s);
      return null;
    }
  }

  Future<Map<String, dynamic>?> getVocabularyById(String word) async {
    try {
      final db = await database;
      final result = await db.query(
        'vocabulary_words',
        where: 'word = ?',
        whereArgs: [word],
        limit: 1,
      );
      return result.isNotEmpty ? result.first : null;
    } catch (e, s) {
      AppLogger.error('Get vocabulary by id error', e, s);
      return null;
    }
  }

  Future<Map<String, dynamic>?> getGrammarById(String ruleId) async {
    try {
      final db = await database;
      final result = await db.query(
        'grammar_rules',
        where: 'rule_id = ?',
        whereArgs: [ruleId],
        limit: 1,
      );
      return result.isNotEmpty ? result.first : null;
    } catch (e, s) {
      AppLogger.error('Get grammar by id error', e, s);
      return null;
    }
  }

  Future<List<Map<String, dynamic>>> searchKanji({
    String? query,
    int? grade,
    int? jlptLevel,
    int? strokeCount,
  }) async {
    try {
      final db = await database;
      String whereClause = '1=1';
      List<dynamic> whereArgs = [];
      
      if (query != null && query.isNotEmpty) {
        whereClause += ' AND (character LIKE ? OR meanings LIKE ? OR onyomi LIKE ? OR kunyomi LIKE ?)';
        whereArgs.addAll(['%$query%', '%$query%', '%$query%', '%$query%']);
      }
      
      if (grade != null) {
        whereClause += ' AND grade = ?';
        whereArgs.add(grade);
      }
      
      if (jlptLevel != null) {
        whereClause += ' AND jlpt_level = ?';
        whereArgs.add(jlptLevel);
      }
      
      if (strokeCount != null) {
        whereClause += ' AND stroke_count = ?';
        whereArgs.add(strokeCount);
      }
      
      return await db.query(
        'kanji_characters',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'grade ASC, character ASC',
      );
    } catch (e, s) {
      AppLogger.error('Search kanji error', e, s);
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> searchVocabulary({
    String? query,
    int? level,
    int? jlptLevel,
    String? partOfSpeech,
  }) async {
    try {
      final db = await database;
      String whereClause = '1=1';
      List<dynamic> whereArgs = [];
      
      if (query != null && query.isNotEmpty) {
        whereClause += ' AND (word LIKE ? OR reading LIKE ? OR meanings LIKE ?)';
        whereArgs.addAll(['%$query%', '%$query%', '%$query%']);
      }
      
      if (level != null) {
        whereClause += ' AND level = ?';
        whereArgs.add(level);
      }
      
      if (jlptLevel != null) {
        whereClause += ' AND jlpt_level = ?';
        whereArgs.add(jlptLevel);
      }
      
      if (partOfSpeech != null && partOfSpeech.isNotEmpty) {
        whereClause += ' AND part_of_speech = ?';
        whereArgs.add(partOfSpeech);
      }
      
      return await db.query(
        'vocabulary_words',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'level ASC, word ASC',
      );
    } catch (e, s) {
      AppLogger.error('Search vocabulary error', e, s);
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> searchGrammar({
    String? query,
    int? level,
    int? jlptLevel,
    String? category,
  }) async {
    try {
      final db = await database;
      String whereClause = '1=1';
      List<dynamic> whereArgs = [];
      
      if (query != null && query.isNotEmpty) {
        whereClause += ' AND (pattern LIKE ? OR meaning LIKE ? OR description LIKE ?)';
        whereArgs.addAll(['%$query%', '%$query%', '%$query%', '%$query%']);
      }
      
      if (level != null) {
        whereClause += ' AND level = ?';
        whereArgs.add(level);
      }
      
      if (jlptLevel != null) {
        whereClause += ' AND jlpt_level = ?';
        whereArgs.add(jlptLevel);
      }
      
      if (category != null && category.isNotEmpty) {
        whereClause += ' AND category = ?';
        whereArgs.add(category);
      }
      
      return await db.query(
        'grammar_rules',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'level ASC, pattern ASC',
      );
    } catch (e, s) {
      AppLogger.error('Search grammar error', e, s);
      return [];
    }
  }

  Future<void> batchInsertKanji(List<Map<String, dynamic>> kanjiList) async {
    try {
      final db = await database;
      await db.transaction((txn) async {
        for (final kanji in kanjiList) {
          await txn.insert('kanji_characters', kanji, conflictAlgorithm: ConflictAlgorithm.replace);
        }
      });
    } catch (e, s) {
      AppLogger.error('Batch insert kanji error', e, s);
    }
  }

  Future<void> batchInsertVocabulary(List<Map<String, dynamic>> vocabularyList) async {
    try {
      final db = await database;
      await db.transaction((txn) async {
        for (final vocabulary in vocabularyList) {
          await txn.insert('vocabulary_words', vocabulary, conflictAlgorithm: ConflictAlgorithm.replace);
        }
      });
    } catch (e, s) {
      AppLogger.error('Batch insert vocabulary error', e, s);
    }
  }

  Future<void> batchInsertGrammar(List<Map<String, dynamic>> grammarList) async {
    try {
      final db = await database;
      await db.transaction((txn) async {
        for (final grammar in grammarList) {
          await txn.insert('grammar_rules', grammar, conflictAlgorithm: ConflictAlgorithm.replace);
        }
      });
    } catch (e, s) {
      AppLogger.error('Batch insert grammar error', e, s);
    }
  }

  Future<int> getKanjiCount() async {
    try {
      final db = await database;
      final result = await db.rawQuery('SELECT COUNT(*) as count FROM kanji_characters');
      return (result.first['count'] as int?) ?? 0;
    } catch (e, s) {
      AppLogger.error('Get kanji count error', e, s);
      return 0;
    }
  }

  Future<int> getVocabularyCount() async {
    try {
      final db = await database;
      final result = await db.rawQuery('SELECT COUNT(*) as count FROM vocabulary_words');
      return (result.first['count'] as int?) ?? 0;
    } catch (e, s) {
      AppLogger.error('Get vocabulary count error', e, s);
      return 0;
    }
  }

  Future<int> getGrammarCount() async {
    try {
      final db = await database;
      final result = await db.rawQuery('SELECT COUNT(*) as count FROM grammar_rules');
      return (result.first['count'] as int?) ?? 0;
    } catch (e, s) {
      AppLogger.error('Get grammar count error', e, s);
      return 0;
    }
  }

  Future<List<Map<String, dynamic>>> getRandomKanji(int count) async {
    try {
      final db = await database;
      return await db.rawQuery('''
        SELECT * FROM kanji_characters 
        ORDER BY RANDOM() 
        LIMIT ?
      ''', [count]);
    } catch (e, s) {
      AppLogger.error('Get random kanji error', e, s);
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> getRandomVocabulary(int count) async {
    try {
      final db = await database;
      return await db.rawQuery('''
        SELECT * FROM vocabulary_words 
        ORDER BY RANDOM() 
        LIMIT ?
      ''', [count]);
    } catch (e, s) {
      AppLogger.error('Get random vocabulary error', e, s);
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> getRandomGrammar(int count) async {
    try {
      final db = await database;
      return await db.rawQuery('''
        SELECT * FROM grammar_rules 
        ORDER BY RANDOM() 
        LIMIT ?
      ''', [count]);
    } catch (e, s) {
      AppLogger.error('Get random grammar error', e, s);
      return [];
    }
  }

  // Tablo varlığını kontrol et
  Future<void> _ensureTablesExist() async {
    try {
      final db = await database;
      
      // Temel tabloların varlığını kontrol et
      final tables = await db.rawQuery("SELECT name FROM sqlite_master WHERE type='table'");
      final tableNames = tables.map((t) => t['name']).toSet();
      
      if (!tableNames.contains('kelimeler')) {
        AppLogger.info('⚠️ Kelimeler tablosu bulunamadı, oluşturuluyor...');
        await _initializationService.initDatabase();
      }
      
      if (!tableNames.contains('kelime_trigrams')) {
        AppLogger.info('⚠️ Trigram tablosu bulunamadı, oluşturuluyor...');
        await _tableCreationService.createAdditionalTables(db);
      }
      
      AppLogger.info('✅ Tablo varlık kontrolü tamamlandı');
    } catch (e, s) {
      AppLogger.error('Tablo varlık kontrolü hatası', e, s);
    }
  }

  // Export user data for sync
  Future<Map<String, dynamic>> exportUserData() async {
    try {
      final db = await database;
      
      // Export user progress and preferences
      final userProgress = await db.query('user_progress');
      final learningStats = await db.query('learning_sessions');
      final preferences = await SharedPreferences.getInstance();
      
      return {
        'user_progress': userProgress,
        'learning_stats': learningStats,
        'preferences': preferences.getKeys().map((key) => {
          'key': key,
          'value': preferences.get(key),
        }).toList(),
        'export_timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e, s) {
      AppLogger.error('Export user data error', e, s);
      return {};
    }
  }

  // Import user data from sync
  Future<void> importUserData(Map<String, dynamic> userData) async {
    try {
      final db = await database;
      
      await db.transaction((txn) async {
        // Import user progress
        if (userData.containsKey('user_progress')) {
          final userProgress = userData['user_progress'] as List;
          for (final progress in userProgress) {
            await txn.insert('user_progress', progress as Map<String, dynamic>,
              conflictAlgorithm: ConflictAlgorithm.replace);
          }
        }
        
        // Import learning stats
        if (userData.containsKey('learning_stats')) {
          final learningStats = userData['learning_stats'] as List;
          for (final stat in learningStats) {
            await txn.insert('learning_sessions', stat as Map<String, dynamic>,
              conflictAlgorithm: ConflictAlgorithm.replace);
          }
        }
      });
      
      // Import preferences
      if (userData.containsKey('preferences')) {
        final preferences = await SharedPreferences.getInstance();
        final prefList = userData['preferences'] as List;
        
        for (final pref in prefList) {
          final prefMap = pref as Map<String, dynamic>;
          final key = prefMap['key'] as String;
          final value = prefMap['value'];
          
          if (value is String) {
            await preferences.setString(key, value);
          } else if (value is int) {
            await preferences.setInt(key, value);
          } else if (value is double) {
            await preferences.setDouble(key, value);
          } else if (value is bool) {
            await preferences.setBool(key, value);
          } else if (value is List<String>) {
            await preferences.setStringList(key, value);
          }
        }
      }
      
      AppLogger.info('✅ User data import completed');
    } catch (e, s) {
      AppLogger.error('Import user data error', e, s);
    }
  }

  // ============ EKSİK METODLAR ============

  /// Analytics event ekle
  Future<void> insertAnalyticsEvent(Map<String, dynamic> event) async {
    try {
      final db = await database;
      await db.insert('analytics_events', event, conflictAlgorithm: ConflictAlgorithm.replace);
      AppLogger.info('Analytics event inserted');
    } catch (e, s) {
      AppLogger.error('Error inserting analytics event', e, s);
    }
  }

  Future<void> insertWord(Map<String, dynamic> word) async {
    // ... existing code ...
  }

  // ============ TRANSLATION SERVICE METHODS ============

  /// Create translation history table
  Future<void> createTranslationHistoryTable() async {
    try {
      final db = await database;
      await db.execute('''
        CREATE TABLE IF NOT EXISTS translation_history (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          query TEXT NOT NULL,
          result_japanese TEXT NOT NULL,
          result_turkish TEXT NOT NULL,
          result_english TEXT,
          result_pronunciation TEXT,
          timestamp TEXT NOT NULL,
          created_at TEXT NOT NULL
        )
      ''');
      AppLogger.info('Translation history table created');
    } catch (e, s) {
      AppLogger.error('Error creating translation history table', e, s);
    }
  }

  /// Create favorites table
  Future<void> createFavoritesTable() async {
    try {
      final db = await database;
      await db.execute('''
        CREATE TABLE IF NOT EXISTS translation_favorites (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          result_id TEXT NOT NULL UNIQUE,
          japanese TEXT NOT NULL,
          turkish TEXT NOT NULL,
          english TEXT,
          pronunciation TEXT,
          created_at TEXT NOT NULL
        )
      ''');
      AppLogger.info('Translation favorites table created');
    } catch (e, s) {
      AppLogger.error('Error creating favorites table', e, s);
    }
  }

  /// Load basic words into cache
  Future<void> loadBasicWords() async {
    try {
      final db = await database;
      final result = await db.query(
        'words',
        limit: 1000,
        orderBy: 'frequency DESC',
      );
      AppLogger.info('Loaded ${result.length} basic words');
    } catch (e, s) {
      AppLogger.error('Error loading basic words', e, s);
    }
  }

  /// Save translation to history
  Future<void> saveTranslationToHistory(String query, dynamic result) async {
    try {
      final db = await database;
      await db.insert(
        'translation_history',
        {
          'query': query,
          'result_japanese': result.japanese ?? '',
          'result_turkish': result.turkish ?? '',
          'result_english': result.english ?? '',
          'result_pronunciation': result.pronunciation ?? '',
          'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
          'created_at': DateTime.now().toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      AppLogger.info('Translation saved to history: $query');
    } catch (e, s) {
      AppLogger.error('Error saving translation to history', e, s);
    }
  }

  /// Add favorite translation
  Future<void> addFavorite(dynamic result) async {
    try {
      final db = await database;
      await db.insert(
        'translation_favorites',
        {
          'result_id': result.id?.toString() ?? DateTime.now().millisecondsSinceEpoch.toString(),
          'japanese': result.japanese ?? '',
          'turkish': result.turkish ?? '',
          'english': result.english ?? '',
          'pronunciation': result.pronunciation ?? '',
          'created_at': DateTime.now().toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      AppLogger.info('Added to favorites');
    } catch (e, s) {
      AppLogger.error('Error adding favorite', e, s);
    }
  }

  /// Remove favorite translation
  Future<void> removeFavorite(String resultId) async {
    try {
      final db = await database;
      await db.delete(
        'translation_favorites',
        where: 'result_id = ?',
        whereArgs: [resultId],
      );
      AppLogger.info('Removed from favorites: $resultId');
    } catch (e, s) {
      AppLogger.error('Error removing favorite', e, s);
    }
  }

  /// Check if translation is favorite
  Future<bool> isFavorite(String resultId) async {
    try {
      final db = await database;
      final result = await db.query(
        'translation_favorites',
        where: 'result_id = ?',
        whereArgs: [resultId],
      );
      return result.isNotEmpty;
    } catch (e, s) {
      AppLogger.error('Error checking favorite status', e, s);
      return false;
    }
  }

  /// Get word count for stats
  Future<int> getWordCount() async {
    try {
      final db = await database;
      final result = await db.rawQuery('SELECT COUNT(*) as count FROM words');
      return (result.first['count'] as int?) ?? 0;
    } catch (e, s) {
      AppLogger.error('Get word count error', e, s);
      return 0;
    }
  }



  Future<List<Word>> getWordsByIds(List<int> ids) async {
    if (ids.isEmpty) return [];
    final db = await database;
    try {
      final List<Map<String, dynamic>> maps = await db.query(
        'words',
        where: 'id IN (${ids.join(',')})',
      );
      return maps.map((map) => Word.fromMap(map)).toList();
    } catch (e) {
      AppLogger.error('Failed to get words by IDs', e);
      return [];
    }
  }

  Future<List<Word>> searchKelime(String query, {int limit = 20}) async {
    final db = await database;
    try {
      final List<Map<String, dynamic>> maps = await db.query(
        'words',
        where: 'meanings_tr LIKE ?',
        whereArgs: ['%$query%'],
        limit: limit,
      );
      return maps.map((map) => Word.fromMap(map)).toList();
    } catch (e) {
      AppLogger.error('Failed to search kelime', e);
      return [];
    }
  }

  Future<int> getFavoriteWordCount() async {
    final db = await database;
    try {
      final result = await db.rawQuery('SELECT COUNT(*) as count FROM words WHERE is_favorite = 1');
      return Sqflite.firstIntValue(result) ?? 0;
    } catch (e) {
      AppLogger.error('Failed to get favorite word count', e);
      return 0;
    }
  }
}