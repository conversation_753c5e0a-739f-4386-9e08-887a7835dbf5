import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';

import '../../models/word.dart';
import '../../services/auth_service.dart';

import '../../widgets/loading_indicator.dart';
import '../../widgets/goals/daily_goals_widget.dart';
import '../../widgets/limits/compact_daily_limits_panel.dart';
import '../../widgets/onboarding/onboarding_controller.dart';
import '../../widgets/error_display.dart';

import 'home_tab_services.dart';
import 'home_tab_data.dart';
import 'home_tab_widgets.dart';
import 'home_tab_utils.dart';
import '../../providers/theme_provider.dart';
import 'package:japoni_go/models/main_grid_item.dart';
import 'package:japoni_go/models/word_of_the_day.dart';
import 'package:japoni_go/widgets/home/<USER>';
import 'package:japoni_go/widgets/home/<USER>';
import 'package:japoni_go/widgets/home/<USER>';

/// State definitions for the home tab
enum HomeTabState { initial, loading, loaded, error }

/// Core home tab widget - main logic and state management
class HomeTabCore extends StatefulWidget {
  final Function(MainGridItem) onGridItemTapped;
  final Function(Word)? onWordSelected;
  final Function()? onNavigateToSearch;
  final Function()? premiumSayfasiAcCallback;
  final Function(String, bool)? kelimeFavoriGuncelleCallback;
  final bool premiumMu;

  const HomeTabCore({
    super.key,
    required this.onGridItemTapped,
    this.onWordSelected,
    this.onNavigateToSearch,
    this.premiumSayfasiAcCallback,
    this.kelimeFavoriGuncelleCallback,
    this.premiumMu = false,
  });

  @override
  State<HomeTabCore> createState() => _HomeTabCoreState();
}

class _HomeTabCoreState extends State<HomeTabCore> 
    with TickerProviderStateMixin, WidgetsBindingObserver {
  
  // Managers
  late final HomeTabServices _services;
  late final HomeTabData _data;
  late final HomeTabWidgets _widgets;
  late final HomeTabUtils _utils;
  
  // State
  HomeTabState _state = HomeTabState.initial;
  String? _errorMessage;
  String? _lastAuthenticatedUserId;

  @override
  void initState() {
    super.initState();
    _initializeManagers();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final authService = Provider.of<AuthService>(context, listen: true);
    if (_lastAuthenticatedUserId != authService.currentUser?.uid) {
      _lastAuthenticatedUserId = authService.currentUser?.uid;
      _loadInitialData();
    }
  }

  /// Initialize managers
  void _initializeManagers() {
    _services = HomeTabServices();
    _data = HomeTabData();
    _widgets = HomeTabWidgets();
    _utils = HomeTabUtils();
  }

  /// Load initial data with robust state management
  Future<void> _loadInitialData() async {
    if (_state == HomeTabState.loading) return;

    setState(() {
      _state = HomeTabState.loading;
      _errorMessage = null;
    });

    try {
      final currentContext = context;

      await Future.microtask(() async {
        if (mounted) {
          await _services.initialize(currentContext);
          await _data.loadInitialData(_services);
          await _utils.initialize();
        }
      });

      if (mounted) {
        setState(() {
          _state = HomeTabState.loaded;
        });
      }

    } catch (e, stackTrace) {
      debugPrint('Error loading initial data: $e\n$stackTrace');
      if (mounted) {
        setState(() {
          _state = HomeTabState.error;
          _errorMessage = "Veriler yüklenemedi. Lütfen internet bağlantınızı kontrol edin ve tekrar deneyin.";
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    switch (_state) {
      case HomeTabState.loading:
      case HomeTabState.initial:
        return const Center(child: LoadingIndicator());
      case HomeTabState.error:
        return Center(
          child: ErrorDisplay(
            message: _errorMessage ?? "Bilinmeyen bir hata oluştu.",
            onRetry: _loadInitialData,
          ),
        );
      case HomeTabState.loaded:
        return RefreshIndicator(
            onRefresh: _loadInitialData,
            child: CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: _buildCompactHeader(context, theme),
                ),
                SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                  sliver: SliverList(
                    delegate: SliverChildListDelegate([
                      _widgets.buildHeaderImage(context, _data, theme),
                      _widgets.buildDailyGoalsSection(context, _data),
                      _widgets.buildWordPackagesSection(context, _data),
                      _widgets.buildHiraganaKatakanaCards(context),
                      if (kDebugMode) ...[
                        const SizedBox(height: 16),
                        const OnboardingDebugWidget(),
                      ],
                      const SizedBox(height: 80),
                    ]),
                  ),
                ),
              ],
            ),
          );
    }
  }

  /// Compact header - Minimum yer kaplayan başlık bölümü
  Widget _buildCompactHeader(BuildContext context, ThemeData theme) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          color: theme.colorScheme.surface,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Auth status - tek satır, minimal (extra dark mode button removed)
              SizedBox(
                height: 40,
                child: Row(
                  children: [
                    Expanded(
                      child: _widgets.buildCompactAuthStatus(context),
                    ),
                    const SizedBox(width: 8),
                  ],
                ),
              ),
              
              // Limits & Goals - tek satır, compact
              SizedBox(
                height: 48, // Fixed height to prevent overflow
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Compact limits
                    Expanded(
                      flex: 3,
                      child: SizedBox(
                        height: 48,
                        child: const CompactDailyLimitsPanel(
                          showTitle: false,
                          showPremiumPrompt: false,
                          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          height: 44,
                        ),
                      ),
                    ),
                    
                    // Compact goals - Authenticated users only
                    Consumer<AuthService>(
                      builder: (context, authService, child) {
                        if (authService.isAuthenticated) {
                          return Expanded(
                            flex: 2,
                            child: SizedBox(
                              height: 48,
                              child: const DailyGoalsWidget(
                                compactMode: true,
                                ultraCompactMode: true,
                              ),
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _utils.dispose();
    _data.dispose();
    _services.dispose();
    super.dispose();
  }
}

Widget _buildWordOfTheDay(
    BuildContext context, WordOfTheDay? wordData, Function(Word)? onWordSelected) {
  if (wordData == null) {
    return const SizedBox.shrink();
  }
  return WordOfTheDayCard(
    word: wordData.word,
    onTap: () {
      if (wordData.word != null) {
        onWordSelected?.call(wordData.word!);
      }
    },
  );
}

Widget _buildQuickAccess(
    BuildContext context, HomeTabServices services, Function(Word)? onWordSelected) {
  return QuickAccessCard(
    title: 'Quick Access',
    subtitle: 'Fast translation',
    icon: Icons.translate,
    onTap: () {
      // Handle quick access tap
    },
  );
}
