import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:provider/provider.dart';
import 'dart:async';

import 'package:japoni_go/utils/app_logger.dart';
import 'package:japoni_go/services/user_progress_service.dart';
import 'package:japoni_go/services/premium_service.dart';
import 'package:japoni_go/providers/theme_provider.dart';
import '../cards/cards_tab_main.dart';
import '../../services/achievement_service.dart';
import '../theme_toggle_button.dart';
import '../animated_theme_container.dart' as theme_container;
import '../../widgets/language_switcher_widget.dart';
import '../../widgets/shimmer_widget.dart';
import '../../services/tema_service.dart';
import '../../widgets/top_bar/top_bar_buttons.dart';
import '../../widgets/global/global_datetime_overlay.dart';

// Screen imports
import '../home_tab.dart';
import '../cards_tab.dart';
import '../dictionary_tab.dart';
import '../games_tab.dart';
import '../flower_shop_screen.dart';
import '../settings_screen.dart';

// Modüler bileşenler
import 'main_screen_services.dart';
import 'main_screen_widgets.dart';
import 'main_screen_navigation.dart';
import 'main_screen_performance.dart';
import 'main_screen_data.dart';

/// Ana ekran core widget - performans odaklı ve modüler
class MainScreenCore extends StatefulWidget {
  const MainScreenCore({super.key});

  @override
  State<MainScreenCore> createState() => _MainScreenCoreState();
}

class _MainScreenCoreState extends State<MainScreenCore> 
    with TickerProviderStateMixin {
  
  // Modüler servis yöneticileri
  late final MainScreenServices _services;
  late final MainScreenWidgets _widgets;
  late final MainScreenNavigation _navigation;
  late final MainScreenPerformance _performance;
  
  // State
  int _selectedIndex = 0;
  bool _hasInternetConnection = true;
  late List<Widget> _widgetOptions;
  bool _widgetOptionsInitialized = false;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;
  StreamSubscription<String>? _languageChangeSubscription;

  @override
  void initState() {
    super.initState();
    _initializeManagers();
    _initializeAnimations();
    _performance.startPerformanceMonitoring();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _initializeServicesAsync();
  }

  /// Yöneticileri başlat
  void _initializeManagers() {
    _services = MainScreenServices();
    _widgets = MainScreenWidgets(
      services: _services,
      data: MainScreenData(),
      onIndexChanged: _onItemTapped,
      onConnectivityChanged: (hasConnection) {
        if (mounted) {
          setState(() {
            _hasInternetConnection = hasConnection;
          });
        }
      },
    );
    _navigation = MainScreenNavigation();
    _performance = MainScreenPerformance();
  }

  /// Animasyonları başlat
  void _initializeAnimations() {
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _fabAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.easeInOut,
    ));
    
    _fabAnimationController.forward();
  }

  /// Servisleri asenkron başlat (ana thread'i bloke etmeden)
  Future<void> _initializeServicesAsync() async {
    if (_widgetOptionsInitialized) return;
    
    // Background'da servisleri başlat
    _performance.executeInBackground(() async {
      await _services.initialize();
      await _initializeWidgetOptions();
      await _checkInternetConnection();
      await _listenToConnectivityChanges();
      await _initializeAchievementService();
      await _setupLanguageChangeListener();
      
      if (mounted) {
        setState(() {
          _widgetOptionsInitialized = true;
        });
      }
    });
  }

  /// Widget seçeneklerini başlat
  Future<void> _initializeWidgetOptions() async {
    if (_widgetOptionsInitialized) return;

    try {
      final currentLanguage = context.locale.languageCode;
      
      _widgetOptions = [
        const HomeTab(),
        const CardsTabMain(),
        const DictionaryTab(),
        const GamesTab(),           // Oyunlar tab'ını ekliyorum
        const FlowerShopScreen(),   // Çiçek mağazası ekliyorum
        const SettingsScreen(),
      ];
      
      _widgetOptionsInitialized = true;
      AppLogger.info('Widget options initialized with ${_widgetOptions.length} tabs');
    } catch (e, s) {
      AppLogger.error('Error initializing widget options', e, s);
      _widgetOptionsInitialized = false;
    }
  }

  /// İnternet bağlantısını kontrol et
  Future<void> _checkInternetConnection() async {
    try {
      final connectivityResults = await Connectivity().checkConnectivity();
      if (mounted) {
        setState(() {
          _hasInternetConnection = _isConnectedList(connectivityResults);
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasInternetConnection = true;
        });
      }
    }
  }

  /// Bağlantı değişikliklerini dinle
  Future<void> _listenToConnectivityChanges() async {
    final Connectivity connectivity = Connectivity();
    connectivity.onConnectivityChanged.listen((result) {
      if (mounted) {
        setState(() {
          _hasInternetConnection = _isConnectedList(result);
        });
      }
    });
  }

  /// Bağlantı durumunu kontrol et
  bool _isConnectedList(List<ConnectivityResult> results) {
    return results.any((result) => 
        result == ConnectivityResult.mobile || 
        result == ConnectivityResult.wifi);
  }

  /// Achievement servisini başlat
  Future<void> _initializeAchievementService() async {
    try {
      // AchievementService initialization handled elsewhere
      AppLogger.info('Achievement service initialized in MainScreen');
    } catch (e, s) {
      AppLogger.error('Error initializing achievement service in MainScreen', e, s);
    }
  }

  /// Dil değişikliği listener'ını kur
  Future<void> _setupLanguageChangeListener() async {
    // Bu fonksiyon MainScreenServices'de implement edilecek
    _languageChangeSubscription = _services.setupLanguageChangeListener(
      onLanguageChanged: (newLanguage) {
        if (mounted) {
          setState(() {
            _widgetOptionsInitialized = false;
          });
          _initializeWidgetOptions().then((_) {
            if (mounted) {
              setState(() {
                _widgetOptionsInitialized = true;
              });
            }
          });
        }
      },
    );
  }

  /// Tab seçimi
  void _onItemTapped(int index) {
    if (_selectedIndex == index) return;

    _services.playClickSound();
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!_widgetOptionsInitialized) {
      return _widgets.buildLoadingScreen(context);
    }

    if (!_hasInternetConnection) {
      return _widgets.buildNoInternetScreen(
        context, 
        onRetry: _checkInternetConnection,
      );
    }

    return GlobalDateTimeOverlay(
      alignment: Alignment.topCenter,
      margin: const EdgeInsets.only(top: 100),
      child: Scaffold(
        appBar: _widgets.buildAppBar(context),
        body: theme_container.AnimatedThemeContainer(
          child: _widgetOptions[_selectedIndex],
        ),
        bottomNavigationBar: _widgets.buildBottomNavigationBar(
          context,
          _selectedIndex,
          _hasInternetConnection,
        ),
        floatingActionButton: _widgets.buildFloatingActionButton(
          context,
          _fabAnimation,
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      ),
    );
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    _languageChangeSubscription?.cancel();
    _performance.dispose();
    _services.dispose();
    super.dispose();
  }
}
